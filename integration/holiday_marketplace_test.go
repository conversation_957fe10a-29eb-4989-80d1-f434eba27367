package integration

import (
	"net/http"
	"testing"

	fastly "github.com/GannettDigital/go-fastly-tests"
)

func TestHolidayMarketplace(t *testing.T) {
	t.<PERSON>()
	var tests = []fastly.Test{
		// HolidayMarketplace endpoints
		fastly.Header{
			Request: fastly.Request{
				Description: "/holiday-marketplace gets a 200 status along with proper headers",
				Method:      "GET",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/holiday-marketplace",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
			Headers: http.Header{
				"Gannett-Debug-Path": []string{
					"holiday-marketplace backend:set",
				},
				"Surrogate-Key": []string{
					"/holiday-marketplace",
				},
				"Cache-Control": []string{
					"max-age=60,no-store",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
	}

	for _, test := range tests {
		test.Execute(t)
	}
}
