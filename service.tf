terraform {
  required_providers {
    fastly = {
      source  = "fastly/fastly"
      version = "6.1.0"
    }
  }
}

variable "fastly_name" {
  type    = string
  default = "www.usatoday.com"
}

variable "FASTLY_MON_BQ_EMAIL" {
  type = string
}

variable "FASTLY_MON_BQ_SECRET_KEY" {
  type = string
}

variable "FASTLY_GCS_EMAIL" {
  type = string
}

variable "FASTLY_GCS_SECRET_KEY" {
  type = string
}

variable "team" {
  type    = string
  default = "SRE"
}

variable "libraries" {
  type    = string
  default = "shared_critical,shared_tangent,shared_prebid,shared_helpers_general,shared_eu,shared_ab_testing,shared_proxy_gannett,shared_proxy_vendor,shared_helpers_frontend,shared_detection"
}

resource "fastly_service_vcl" "fastly" {

  name = terraform.workspace == "production" ? var.fastly_name : format("%s-%s", terraform.workspace, var.fastly_name)

  comment = format("Managed by %s @ https://github.com/GannettDigital/paas-vcl-%s; %s; %s", var.team, var.fastly_name, var.libraries, terraform.workspace)

  # Main domain, changes depending on environment.
  # For non-production environment, prepend environment name. ie: staging-www.usatoday.com
  domain {
    name = terraform.workspace == "production" ? var.fastly_name : format("%s-%s", terraform.workspace, var.fastly_name)
  }

  domain {
    name = terraform.workspace == "production" ? "usatoday.com" : format("%s-%s", terraform.workspace, "usatoday.com")
  }

  domain {
    name = terraform.workspace == "production" ? "www.usat.com" : format("%s-%s", terraform.workspace, "www.usat.com")
  }

  domain {
    name = terraform.workspace == "production" ? "www.usatoday.net" : format("%s-%s", terraform.workspace, "www.usatoday.net")
  }

  domain {
    name = terraform.workspace == "production" ? "sportspolls.usatoday.com" : format("%s-%s", terraform.workspace, "sportspolls.usatoday.com")
  }

  domain {
    name = terraform.workspace == "production" ? "www.usatodaynetworkservice.com" : format("%s-%s", terraform.workspace, "www.usatodaynetworkservice.com")
  }

  domain {
    name = terraform.workspace == "production" ? "usatodaynetworkservice.com" : format("%s-%s", terraform.workspace, "usatodaynetworkservice.com")
  }

  domain {
    name = terraform.workspace == "production" ? "gaming.usatoday.com" : format("%s-%s", terraform.workspace, "gaming.usatoday.com")
  }

  domain {
    name = terraform.workspace == "production" ? "usatodaysportsplus.com" : format("%s-%s", terraform.workspace, "usatodaysportsplus.com")
  }

  domain {
    name = terraform.workspace == "production" ? "www.usatodaysportsplus.com" : format("%s-%s", terraform.workspace, "www.usatodaysportsplus.com")
  }

  condition {
    name      = "no_default_host"
    statement = "!req.url"
    priority  = 0
    type      = "REQUEST"
  }

  # we still need to configure it so vcl can hook into this logger
  condition {
    name      = "no_logging"
    statement = "!req.url"
    priority  = 0
    type      = "RESPONSE"
  }

  backend {
    address               = terraform.workspace == "origin-staging-2" ? "usat-staging.mktplatforms.com" : "usat-prod.mktplatforms.com"
    name                  = "blueprint-backend"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    shield                = "iad-va-us"
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    ssl_sni_hostname      = terraform.workspace == "origin-staging-2" ? "usat-staging.mktplatforms.com" : "usat-prod.mktplatforms.com"
    ssl_cert_hostname     = terraform.workspace == "origin-staging-2" ? "usat-staging.mktplatforms.com" : "usat-prod.mktplatforms.com"
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 6000
  }

  backend {
    address               = terraform.workspace == "staging" ? "comics-staging-origin.gannettdigital.com" : "comics-east-origin.gannettdigital.com"
    name                  = "comics-east"
    shield                = "iad-va-us"
    healthcheck           = "comics-east-healthcheck"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = "*.usatoday.com"
    ssl_cert_hostname     = "*.usatoday.com"
    request_condition     = "no_default_host"
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 8000
    auto_loadbalance      = false
  }

  healthcheck {
    name              = "comics-east-healthcheck"
    host              = terraform.workspace == "staging" ? "origin-staging-www.usatoday.com" : "www.usatoday.com"
    path              = "/comics/health-check"
    expected_response = 200
    method            = "GET"
    check_interval    = 60000
    threshold         = 1
    timeout           = 5000
    window            = 2
    initial           = 1
  }

  backend {
    address               = terraform.workspace == "staging" ? "comics-staging-origin.gannettdigital.com" : "comics-west-origin.gannettdigital.com"
    name                  = "comics-west"
    shield                = "iad-va-us"
    healthcheck           = "comics-west-healthcheck"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = "*.usatoday.com"
    ssl_cert_hostname     = "*.usatoday.com"
    request_condition     = "no_default_host"
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 8000
    auto_loadbalance      = false
  }

  healthcheck {
    name              = "comics-west-healthcheck"
    host              = terraform.workspace == "staging" ? "origin-staging-www.usatoday.com" : "www.usatoday.com"
    path              = "/comics/health-check"
    expected_response = 200
    method            = "GET"
    check_interval    = 60000
    threshold         = 1
    timeout           = 5000
    window            = 2
    initial           = 1
  }

  backend {
    address               = "d2x1ti9cf8k8y9.cloudfront.net"
    name                  = "deals-backend"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    shield                = "iad-va-us"
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    ssl_sni_hostname      = "d2x1ti9cf8k8y9.cloudfront.net"
    ssl_cert_hostname     = "d2x1ti9cf8k8y9.cloudfront.net"
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 6000
  }

  # gcias Backend - #NO SHIELD
  backend {
    address               = "gcias-compute.usatoday.com"
    name                  = "gcias-compute.usatoday.com"
    port                  = 443
    use_ssl               = true
    shield                = "iad-va-us"
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    ssl_cert_hostname     = "gcias-compute.usatoday.com"
    ssl_sni_hostname      = "gcias-compute.usatoday.com"
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 6000
  }
  # gcias staging Backend - #NO SHIELD
  backend {
    address               = "staging-gcias-compute.usatoday.com"
    name                  = "staging-gcias-compute.usatoday.com"
    port                  = 443
    use_ssl               = true
    shield                = "iad-va-us"
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    ssl_cert_hostname     = "staging-gcias-compute.usatoday.com"
    ssl_sni_hostname      = "staging-gcias-compute.usatoday.com"
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 6000
  }
  # gcias ci Backend - #NO SHIELD
  backend {
    address               = "ci-gcias-compute.usatoday.com"
    name                  = "ci-gcias-compute.usatoday.com"
    port                  = 443
    use_ssl               = true
    shield                = "iad-va-us"
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    ssl_cert_hostname     = "ci-gcias-compute.usatoday.com"
    ssl_sni_hostname      = "ci-gcias-compute.usatoday.com"
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 6000
  }

  backend {
    # /booklist east backend
    address               = terraform.workspace == "origin-staging" ? "booklist-us-east1-7tpppv3ona-ue.a.run.app" : "booklist-us-east1-suh2mjo3oa-ue.a.run.app"
    name                  = "booklist-east"
    shield                = "iad-va-us"
    healthcheck           = "booklist-east-healthcheck"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = "*.usatoday.com"
    ssl_cert_hostname     = "*.a.run.app"
    request_condition     = "no_default_host"
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 8000
    auto_loadbalance      = false
  }

  backend {
    # /booklist west backend
    address               = terraform.workspace == "origin-staging" ? "booklist-us-east1-7tpppv3ona-ue.a.run.app" : "booklist-us-west1-suh2mjo3oa-uw.a.run.app"
    name                  = "booklist-west"
    shield                = "iad-va-us"
    healthcheck           = "booklist-west-healthcheck"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = "*.usatoday.com"
    ssl_cert_hostname     = "*.a.run.app"
    request_condition     = "no_default_host"
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 8000
    auto_loadbalance      = false
  }

  backend {
    # Sports Contests Cloud Run East backend
    address               = terraform.workspace == "origin-staging" ? "dx-games-live-us-east1-45898334026.us-east1.run.app" : "dx-games-live-us-east1-664912083968.us-east1.run.app"
    name                  = "sportscontests-east"
    shield                = "iad-va-us"
    healthcheck           = "sportscontests-east-healthcheck"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = "*.a.run.app"
    ssl_cert_hostname     = "*.a.run.app"
    request_condition     = "no_default_host"
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 8000
    auto_loadbalance      = false
  }

  backend {
    # Sports Contests Cloud Run west backend
    address               = terraform.workspace == "origin-staging" ? "dx-games-live-us-east1-45898334026.us-east1.run.app" : "dx-games-live-us-west1-664912083968.us-west1.run.app"
    name                  = "sportscontests-west"
    shield                = "bfi-wa-us"
    healthcheck           = "sportscontests-west-healthcheck"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = "*.a.run.app"
    ssl_cert_hostname     = "*.a.run.app"
    request_condition     = "no_default_host"
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 8000
    auto_loadbalance      = false
  }

  backend {
    address               = "vip-lb.wordpress.com"
    name                  = "vip-lb.wordpress.com"
    shield                = "iad-va-us"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = "sportspolls.usatoday.com"
    ssl_cert_hostname     = "vip-lb.wordpress.com"
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 6000
  }

  backend {
    address               = terraform.workspace == "ci" ? "closed-pepper-u2frfipet9imfshj8u585qhj.herokudns.com" : "evening-prawn-fdueu2e8qrksiv9bqhobs1jr.herokudns.com"
    name                  = "gaming-chalkline"
    shield                = "iad-va-us"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = "gaming.usatoday.com"
    ssl_cert_hostname     = "gaming.usatoday.com"
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 6000
  }

  # Google Retail API backend - #NO SHIELD
  backend {
    address               = "retail.googleapis.com"
    name                  = "retail.googleapis.com"
    shield                = "iad-va-us"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = "retail.googleapis.com"
    ssl_cert_hostname     = "retail.googleapis.com"
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 6000
  }

  # GUPAS endpoint used by the WALL*LY CAM code
  backend {
    address               = terraform.workspace == "origin-staging" ? "gupas-staging.gannettdigital.com" : "gupas.gannettdigital.com"
    name                  = "GUPAS"
    shield                = "iad-va-us"
    healthcheck           = "GUPAS-healthcheck"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = terraform.workspace == "origin-staging" ? "gupas-staging.gannettdigital.com" : "gupas.gannettdigital.com"
    ssl_cert_hostname     = "*.gannettdigital.com"
    request_condition     = "no_default_host"
    between_bytes_timeout = 1000
    first_byte_timeout    = 1000
    connect_timeout       = 1000
    auto_loadbalance      = false
  }

  backend {
    address               = terraform.workspace == "origin-staging-2" ? "appops-tt-staging-us-east1-common-ingress-1.gannettdigital.com" : "appops-tt-production-us-east1-common-ingress-1.gannettdigital.com"
    name                  = "realestate-east"
    shield                = "iad-va-us"
    healthcheck           = "realestate-east-healthcheck"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = "*.gannettdigital.com"
    ssl_cert_hostname     = "*.gannettdigital.com"
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 6000
  }

  healthcheck {
    name              = "realestate-east-healthcheck"
    host              = terraform.workspace == "origin-staging-2" ? "appops-tt-staging-us-east1-common-ingress-1.gannettdigital.com" : "appops-tt-production-us-east1-common-ingress-1.gannettdigital.com"
    path              = "/real-estate/health-check"
    expected_response = 200
    method            = "GET"
    check_interval    = 60000
    threshold         = 1
    timeout           = 5000
    window            = 2
    initial           = 1
  }

  backend {
    address               = terraform.workspace == "origin-staging-2" ? "appops-tt-staging-us-east1-common-ingress-1.gannettdigital.com" : "appops-tt-production-us-west1-common-ingress-1.gannettdigital.com"
    name                  = "realestate-west"
    shield                = "iad-va-us"
    healthcheck           = "realestate-west-healthcheck"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = "*.gannettdigital.com"
    ssl_cert_hostname     = "*.gannettdigital.com"
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 6000
  }

  healthcheck {
    name              = "realestate-west-healthcheck"
    host              = terraform.workspace == "origin-staging-2" ? "appops-tt-staging-us-east1-common-ingress-1.gannettdigital.com" : "appops-tt-production-us-west1-common-ingress-1.gannettdigital.com"
    path              = "/real-estate/health-check"
    expected_response = 200
    method            = "GET"
    check_interval    = 60000
    threshold         = 1
    timeout           = 5000
    window            = 2
    initial           = 1
  }

  backend {
    address               = terraform.workspace == "origin-staging" ? "dev-origin-tailgating.usatoday.com" : "origin-east-tailgating.usatoday.com"
    name                  = "tailgating-east"
    shield                = "iad-va-us"
    healthcheck           = "tailgating-east-healthcheck"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = "*.gannettdigital.com"
    ssl_cert_hostname     = "*.gannettdigital.com"
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 6000
  }

  healthcheck {
    name              = "tailgating-east-healthcheck"
    host              = terraform.workspace == "origin-staging" ? "dev-origin-tailgating.usatoday.com" : "origin-east-tailgating.usatoday.com"
    path              = "/great-american-tailgate/health-check"
    expected_response = 200
    method            = "GET"
    check_interval    = 60000
    threshold         = 1
    timeout           = 5000
    window            = 2
    initial           = 1
  }

  backend {
    address               = terraform.workspace == "origin-staging" ? "dev-origin-tailgating.usatoday.com" : "origin-west-tailgating.usatoday.com"
    name                  = "tailgating-west"
    shield                = "iad-va-us"
    healthcheck           = "tailgating-west-healthcheck"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = "*.gannettdigital.com"
    ssl_cert_hostname     = "*.gannettdigital.com"
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 6000
  }

  healthcheck {
    name              = "tailgating-west-healthcheck"
    host              = terraform.workspace == "origin-staging" ? "dev-origin-tailgating.usatoday.com" : "origin-west-tailgating.usatoday.com"
    path              = "/great-american-tailgate/health-check"
    expected_response = 200
    method            = "GET"
    check_interval    = 60000
    threshold         = 1
    timeout           = 5000
    window            = 2
    initial           = 1
  }

  backend {
    address               = terraform.workspace == "origin-staging" ? "appops-tt-staging-us-east1-common-ingress-1.gannettdigital.com" : "appops-tt-production-us-east1-common-ingress-1.gannettdigital.com"
    name                  = "vacation-east"
    shield                = "iad-va-us"
    healthcheck           = "vacation-east-healthcheck"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = "*.gannettdigital.com"
    ssl_cert_hostname     = "*.gannettdigital.com"
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 6000
  }

  healthcheck {
    name              = "vacation-east-healthcheck"
    host              = terraform.workspace == "origin-staging" ? "appops-tt-staging-us-east1-common-ingress-1.gannettdigital.com" : "appops-tt-production-us-east1-common-ingress-1.gannettdigital.com"
    path              = "/great-american-vacation/health-check"
    expected_response = 200
    method            = "GET"
    check_interval    = 60000
    threshold         = 1
    timeout           = 5000
    window            = 2
    initial           = 1
  }

  backend {
    address               = terraform.workspace == "origin-staging" ? "appops-tt-staging-us-east1-common-ingress-1.gannettdigital.com" : "appops-tt-production-us-west1-common-ingress-1.gannettdigital.com"
    name                  = "vacation-west"
    shield                = "iad-va-us"
    healthcheck           = "vacation-west-healthcheck"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = "*.gannettdigital.com"
    ssl_cert_hostname     = "*.gannettdigital.com"
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 6000
  }

  healthcheck {
    name              = "vacation-west-healthcheck"
    host              = terraform.workspace == "origin-staging" ? "appops-tt-staging-us-east1-common-ingress-1.gannettdigital.com" : "appops-tt-production-us-west1-common-ingress-1.gannettdigital.com"
    path              = "/great-american-vacation/health-check"
    expected_response = 200
    method            = "GET"
    check_interval    = 60000
    threshold         = 1
    timeout           = 5000
    window            = 2
    initial           = 1
  }

  backend {
    address               = terraform.workspace == "origin-staging-1" ? "appops-tt-staging-us-east1-common-ingress-1.gannettdigital.com" : "appops-tt-production-us-east1-common-ingress-1.gannettdigital.com"
    name                  = "advertise-with-us-east"
    shield                = "iad-va-us"
    healthcheck           = "advertise-with-us-east-healthcheck"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = "*.gannettdigital.com"
    ssl_cert_hostname     = "*.gannettdigital.com"
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 6000
  }

  healthcheck {
    name              = "advertise-with-us-east-healthcheck"
    host              = terraform.workspace == "origin-staging-1" ? "appops-tt-staging-us-east1-common-ingress-1.gannettdigital.com" : "appops-tt-production-us-east1-common-ingress-1.gannettdigital.com"
    path              = "/group-subscriptions-corporate/health-check"
    expected_response = 200
    method            = "GET"
    check_interval    = 60000
    threshold         = 1
    timeout           = 5000
    window            = 2
    initial           = 1
  }

  backend {
    address               = terraform.workspace == "origin-staging-1" ? "appops-tt-staging-us-east1-common-ingress-1.gannettdigital.com" : "appops-tt-production-us-west1-common-ingress-1.gannettdigital.com"
    name                  = "advertise-with-us-west"
    shield                = "iad-va-us"
    healthcheck           = "advertise-with-us-west-healthcheck"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = "*.gannettdigital.com"
    ssl_cert_hostname     = "*.gannettdigital.com"
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 6000
  }

  healthcheck {
    name              = "advertise-with-us-west-healthcheck"
    host              = terraform.workspace == "origin-staging-1" ? "appops-tt-staging-us-east1-common-ingress-1.gannettdigital.com" : "appops-tt-production-us-west1-common-ingress-1.gannettdigital.com"
    path              = "/group-subscriptions-corporate/health-check"
    expected_response = 200
    method            = "GET"
    check_interval    = 60000
    threshold         = 1
    timeout           = 5000
    window            = 2
    initial           = 1
  }

  backend {
    address               = terraform.workspace == "origin-staging-2" ? "dev-origin-woty.usatoday.com" : "origin-east-woty.usatoday.com"
    name                  = "woty-east"
    shield                = "iad-va-us"
    healthcheck           = "woty-east-healthcheck"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = "*.gannettdigital.com"
    ssl_cert_hostname     = "*.gannettdigital.com"
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 6000
  }

  healthcheck {
    name              = "woty-east-healthcheck"
    host              = terraform.workspace == "origin-staging-2" ? "dev-origin-woty.usatoday.com" : "origin-east-woty.usatoday.com"
    path              = "/women-of-the-year-2024/health-check"
    expected_response = 200
    method            = "GET"
    check_interval    = 60000
    threshold         = 1
    timeout           = 5000
    window            = 2
    initial           = 1
  }

  backend {
    address               = terraform.workspace == "origin-staging-2" ? "dev-origin-woty.usatoday.com" : "origin-west-woty.usatoday.com"
    name                  = "woty-west"
    shield                = "iad-va-us"
    healthcheck           = "woty-west-healthcheck"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = "*.gannettdigital.com"
    ssl_cert_hostname     = "*.gannettdigital.com"
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 6000
  }

  healthcheck {
    name              = "woty-west-healthcheck"
    host              = terraform.workspace == "origin-staging-2" ? "dev-origin-woty.usatoday.com" : "origin-west-woty.usatoday.com"
    path              = "/women-of-the-year-2024/health-check"
    expected_response = 200
    method            = "GET"
    check_interval    = 60000
    threshold         = 1
    timeout           = 5000
    window            = 2
    initial           = 1
  }

  backend {
    address               = terraform.workspace == "origin-staging-2" ? "appops-tt-staging-us-east1-common-ingress-1.gannettdigital.com" : "appops-tt-production-us-east1-common-ingress-1.gannettdigital.com"
    name                  = "woty2025-east"
    shield                = "iad-va-us"
    healthcheck           = "woty2025-east-healthcheck"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = "*.gannettdigital.com"
    ssl_cert_hostname     = "*.gannettdigital.com"
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 6000
  }

  healthcheck {
    name              = "woty2025-east-healthcheck"
    host              = terraform.workspace == "origin-staging-2" ? "appops-tt-staging-us-east1-common-ingress-1.gannettdigital.com" : "appops-tt-production-us-east1-common-ingress-1.gannettdigital.com"
    path              = "/women-of-the-year-2025/health-check"
    expected_response = 200
    method            = "GET"
    check_interval    = 60000
    threshold         = 1
    timeout           = 5000
    window            = 2
    initial           = 1
  }

  backend {
    address               = terraform.workspace == "origin-staging-2" ? "appops-tt-staging-us-east1-common-ingress-1.gannettdigital.com" : "appops-tt-production-us-west1-common-ingress-1.gannettdigital.com"
    name                  = "woty2025-west"
    shield                = "iad-va-us"
    healthcheck           = "woty2025-west-healthcheck"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = "*.gannettdigital.com"
    ssl_cert_hostname     = "*.gannettdigital.com"
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 6000
  }

  healthcheck {
    name              = "woty2025-west-healthcheck"
    host              = terraform.workspace == "origin-staging-2" ? "appops-tt-staging-us-east1-common-ingress-1.gannettdigital.com" : "appops-tt-production-us-west1-common-ingress-1.gannettdigital.com"
    path              = "/women-of-the-year-2025/health-check"
    expected_response = 200
    method            = "GET"
    check_interval    = 60000
    threshold         = 1
    timeout           = 5000
    window            = 2
    initial           = 1
  }

  backend {
    address               = terraform.workspace == "origin-staging-2" ? "appops-tt-staging-us-east1-common-ingress-1.gannettdigital.com" : "appops-tt-production-us-east1-common-ingress-1.gannettdigital.com"
    name                  = "womenssports-east"
    shield                = "iad-va-us"
    healthcheck           = "womenssports-east-healthcheck"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = "*.gannettdigital.com"
    ssl_cert_hostname     = "*.gannettdigital.com"
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 6000
  }

  healthcheck {
    name              = "womenssports-east-healthcheck"
    host              = terraform.workspace == "origin-staging-2" ? "appops-tt-staging-us-east1-common-ingress-1.gannettdigital.com" : "appops-tt-production-us-east1-common-ingress-1.gannettdigital.com"
    path              = "/womens-sports/health-check"
    expected_response = 200
    method            = "GET"
    check_interval    = 60000
    threshold         = 1
    timeout           = 5000
    window            = 2
    initial           = 1
  }

  backend {
    address               = terraform.workspace == "origin-staging-2" ? "appops-tt-staging-us-east1-common-ingress-1.gannettdigital.com" : "appops-tt-production-us-west1-common-ingress-1.gannettdigital.com"
    name                  = "womenssports-west"
    shield                = "iad-va-us"
    healthcheck           = "womenssports-west-healthcheck"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = "*.gannettdigital.com"
    ssl_cert_hostname     = "*.gannettdigital.com"
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 6000
  }

  healthcheck {
    name              = "womenssports-west-healthcheck"
    host              = terraform.workspace == "origin-staging-2" ? "appops-tt-staging-us-east1-common-ingress-1.gannettdigital.com" : "appops-tt-production-us-west1-common-ingress-1.gannettdigital.com"
    path              = "/womens-sports/health-check"
    expected_response = 200
    method            = "GET"
    check_interval    = 60000
    threshold         = 1
    timeout           = 5000
    window            = 2
    initial           = 1
  }

  backend {
    address               = terraform.workspace == "origin-staging" ? "dev-origin-holidaymarketplace.usatoday.com" : "origin-east-holidaymarketplace.usatoday.com"
    name                  = "holidaymarketplace-east"
    healthcheck           = "holidaymarketplace-east-healthcheck"
    shield                = "iad-va-us"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = terraform.workspace == "origin-staging" ? "dev-origin-holidaymarketplace.usatoday.com" : "origin-east-holidaymarketplace.usatoday.com"
    ssl_cert_hostname     = terraform.workspace == "origin-staging" ? "dev-origin-holidaymarketplace.usatoday.com" : "origin-east-holidaymarketplace.usatoday.com"
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 6000
  }

  healthcheck {
    name              = "holidaymarketplace-east-healthcheck"
    host              = terraform.workspace == "origin-staging" ? "dev-origin-holidaymarketplace.usatoday.com" : "origin-east-holidaymarketplace.usatoday.com"
    path              = "/holiday-marketplace/health-check"
    expected_response = 200
    method            = "GET"
    check_interval    = 60000
    threshold         = 1
    timeout           = 5000
    window            = 2
    initial           = 1
  }

  backend {
    address               = terraform.workspace == "origin-staging" ? "dev-origin-holidaymarketplace.usatoday.com" : "origin-west-holidaymarketplace.usatoday.com"
    name                  = "holidaymarketplace-west"
    healthcheck           = "holidaymarketplace-west-healthcheck"
    shield                = "iad-va-us"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = terraform.workspace == "origin-staging" ? "dev-origin-holidaymarketplace.usatoday.com" : "origin-west-holidaymarketplace.usatoday.com"
    ssl_cert_hostname     = terraform.workspace == "origin-staging" ? "dev-origin-holidaymarketplace.usatoday.com" : "origin-west-holidaymarketplace.usatoday.com"
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 6000
  }

  healthcheck {
    name              = "holidaymarketplace-west-healthcheck"
    host              = terraform.workspace == "origin-staging" ? "dev-origin-holidaymarketplace.usatoday.com" : "origin-west-holidaymarketplace.usatoday.com"
    path              = "/holiday-marketplace/health-check"
    expected_response = 200
    method            = "GET"
    check_interval    = 60000
    threshold         = 1
    timeout           = 5000
    window            = 2
    initial           = 1
  }

  backend {
    address               = terraform.workspace == "origin-staging-2" ? "appops-tt-staging-us-east1-common-ingress-1.gannettdigital.com" : "appops-tt-production-us-east1-common-ingress-1.gannettdigital.com"
    name                  = "home-improvement-east"
    shield                = "iad-va-us"
    healthcheck           = "home-improvement-east-healthcheck"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = "*.gannettdigital.com"
    ssl_cert_hostname     = "*.gannettdigital.com"
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 6000
  }

  healthcheck {
    name              = "home-improvement-east-healthcheck"
    host              = terraform.workspace == "origin-staging-2" ? "appops-tt-staging-us-east1-common-ingress-1.gannettdigital.com" : "appops-tt-production-us-east1-common-ingress-1.gannettdigital.com"
    path              = "/home-improvement/health-check"
    expected_response = 200
    method            = "GET"
    check_interval    = 60000
    threshold         = 1
    timeout           = 5000
    window            = 2
    initial           = 1
  }

  backend {
    address               = terraform.workspace == "origin-staging-2" ? "appops-tt-staging-us-east1-common-ingress-1.gannettdigital.com" : "appops-tt-production-us-west1-common-ingress-1.gannettdigital.com"
    name                  = "home-improvement-west"
    shield                = "iad-va-us"
    healthcheck           = "home-improvement-west-healthcheck"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = "*.gannettdigital.com"
    ssl_cert_hostname     = "*.gannettdigital.com"
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 6000
  }

  healthcheck {
    name              = "home-improvement-west-healthcheck"
    host              = terraform.workspace == "origin-staging-2" ? "appops-tt-staging-us-east1-common-ingress-1.gannettdigital.com" : "appops-tt-production-us-west1-common-ingress-1.gannettdigital.com"
    path              = "/home-improvement/health-check"
    expected_response = 200
    method            = "GET"
    check_interval    = 60000
    threshold         = 1
    timeout           = 5000
    window            = 2
    initial           = 1
  }

  # Sports Hubs
  backend {
    address               = terraform.workspace == "origin-staging-2" ? "origin-staging-2-sportshubs.usatoday.com" : "sportshubs.usatoday.com"
    name                  = "sports-hubs-east"
    shield                = "iad-va-us"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = "*.usatoday.com"
    ssl_cert_hostname     = "*.usatoday.com"
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 6000
  }

  # petshub backend
  backend {
    address               = terraform.workspace == "origin-staging" ? "appops-tt-staging-us-east1-common-ingress-1.gannettdigital.com" : "appops-tt-production-us-east1-common-ingress-1.gannettdigital.com"
    name                  = "petshub-east"
    shield                = "iad-va-us"
    healthcheck           = "petshub-east-healthcheck"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = "*.gannettdigital.com"
    ssl_cert_hostname     = "*.gannettdigital.com"
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 6000
  }

  healthcheck {
    name              = "petshub-east-healthcheck"
    host              = terraform.workspace == "origin-staging" ? "appops-tt-staging-us-east1-common-ingress-1.gannettdigital.com" : "appops-tt-production-us-east1-common-ingress-1.gannettdigital.com"
    path              = "/pets-animals/health-check"
    expected_response = 200
    method            = "GET"
    check_interval    = 60000
    threshold         = 1
    timeout           = 5000
    window            = 2
    initial           = 1
  }

  backend {
    address               = terraform.workspace == "origin-staging" ? "appops-tt-staging-us-east1-common-ingress-1.gannettdigital.com" : "appops-tt-production-us-west1-common-ingress-1.gannettdigital.com"
    name                  = "petshub-west"
    shield                = "bfi-wa-us"
    healthcheck           = "petshub-west-healthcheck"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = "*.gannettdigital.com"
    ssl_cert_hostname     = "*.gannettdigital.com"
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 6000
  }

  healthcheck {
    name              = "petshub-west-healthcheck"
    host              = terraform.workspace == "origin-staging" ? "appops-tt-staging-us-east1-common-ingress-1.gannettdigital.com" : "appops-tt-production-us-west1-common-ingress-1.gannettdigital.com"
    path              = "/pets-animals/health-check"
    expected_response = 200
    method            = "GET"
    check_interval    = 60000
    threshold         = 1
    timeout           = 5000
    window            = 2
    initial           = 1
  }

  # celebrityhub backend
  backend {
    address               = terraform.workspace == "origin-staging-1" ? "appops-tt-staging-us-east1-common-ingress-1.gannettdigital.com" : "appops-tt-production-us-east1-common-ingress-1.gannettdigital.com"
    name                  = "celebrityhub-east"
    shield                = "iad-va-us"
    healthcheck           = "celebrityhub-east-healthcheck"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = "*.gannettdigital.com"
    ssl_cert_hostname     = "*.gannettdigital.com"
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 6000
  }

  healthcheck {
    name              = "celebrityhub-east-healthcheck"
    host              = terraform.workspace == "origin-staging-1" ? "appops-tt-staging-us-east1-common-ingress-1.gannettdigital.com" : "appops-tt-production-us-east1-common-ingress-1.gannettdigital.com"
    path              = "/entertainment/music/health-check"
    expected_response = 200
    method            = "GET"
    check_interval    = 60000
    threshold         = 1
    timeout           = 5000
    window            = 2
    initial           = 1
  }

  backend {
    address               = terraform.workspace == "origin-staging-1" ? "appops-tt-staging-us-east1-common-ingress-1.gannettdigital.com" : "appops-tt-production-us-west1-common-ingress-1.gannettdigital.com"
    name                  = "celebrityhub-west"
    shield                = "bfi-wa-us"
    healthcheck           = "celebrityhub-west-healthcheck"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = "*.gannettdigital.com"
    ssl_cert_hostname     = "*.gannettdigital.com"
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 6000
  }

  healthcheck {
    name              = "celebrityhub-west-healthcheck"
    host              = terraform.workspace == "origin-staging-1" ? "appops-tt-staging-us-east1-common-ingress-1.gannettdigital.com" : "appops-tt-production-us-west1-common-ingress-1.gannettdigital.com"
    path              = "/entertainment/music/health-check"
    expected_response = 200
    method            = "GET"
    check_interval    = 60000
    threshold         = 1
    timeout           = 5000
    window            = 2
    initial           = 1
  }

  # /bot-detection backend
  backend {
    address               = terraform.workspace == "staging" ? "appops-tt-staging-us-east1-common-ingress-1.gannettdigital.com" : "appops-tt-production-us-east1-common-ingress-1.gannettdigital.com"
    name                  = "bot-detection-east"
    shield                = "iad-va-us"
    healthcheck           = "bot-detection-east-healthcheck"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = "*.gannettdigital.com"
    ssl_cert_hostname     = "*.gannettdigital.com"
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 6000
  }

  healthcheck {
    name              = "bot-detection-east-healthcheck"
    host              = terraform.workspace == "staging" ? "appops-tt-staging-us-east1-common-ingress-1.gannettdigital.com" : "appops-tt-production-us-east1-common-ingress-1.gannettdigital.com"
    path              = "/bot-detection/health-check"
    expected_response = 200
    method            = "GET"
    check_interval    = 60000
    threshold         = 1
    timeout           = 5000
    window            = 2
    initial           = 1
  }

  backend {
    address               = terraform.workspace == "staging" ? "appops-tt-staging-us-east1-common-ingress-1.gannettdigital.com" : "appops-tt-production-us-west1-common-ingress-1.gannettdigital.com"
    name                  = "bot-detection-west"
    shield                = "bfi-wa-us"
    healthcheck           = "bot-detection-west-healthcheck"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = "*.gannettdigital.com"
    ssl_cert_hostname     = "*.gannettdigital.com"
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 6000
  }

  healthcheck {
    name              = "bot-detection-west-healthcheck"
    host              = terraform.workspace == "staging" ? "appops-tt-staging-us-east1-common-ingress-1.gannettdigital.com" : "appops-tt-production-us-west1-common-ingress-1.gannettdigital.com"
    path              = "/bot-detection/health-check"
    expected_response = 200
    method            = "GET"
    check_interval    = 60000
    threshold         = 1
    timeout           = 5000
    window            = 2
    initial           = 1
  }

  backend {
    # /lottery east backend
    address               = terraform.workspace == "origin-staging-1" ? "lottery-staging-origin.gannettdigital.com" : "lottery-east-origin.gannettdigital.com"
    name                  = "lottery-east"
    shield                = "iad-va-us"
    healthcheck           = "lottery-east-healthcheck"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = "*.usatoday.com"
    ssl_cert_hostname     = "*.usatoday.com"
    request_condition     = "no_default_host"
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 8000
    auto_loadbalance      = false
  }

  healthcheck {
    name              = "lottery-east-healthcheck"
    host              = terraform.workspace == "origin-staging-1" ? "origin-staging-1-www.usatoday.com" : "www.usatoday.com"
    path              = "/lottery/health-check"
    expected_response = 200
    method            = "GET"
    check_interval    = 60000
    threshold         = 1
    timeout           = 5000
    window            = 2
    initial           = 1
  }

  backend {
    # /lottery west backend
    address               = terraform.workspace == "origin-staging-1" ? "lottery-staging-origin.gannettdigital.com" : "lottery-west-origin.gannettdigital.com"
    name                  = "lottery-west"
    shield                = "iad-va-us"
    healthcheck           = "lottery-west-healthcheck"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = "*.usatoday.com"
    ssl_cert_hostname     = "*.usatoday.com"
    request_condition     = "no_default_host"
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 8000
    auto_loadbalance      = false
  }

  healthcheck {
    name              = "lottery-west-healthcheck"
    host              = terraform.workspace == "origin-staging-1" ? "origin-staging-1-www.usatoday.com" : "www.usatoday.com"
    path              = "/lottery/health-check"
    expected_response = 200
    method            = "GET"
    check_interval    = 60000
    threshold         = 1
    timeout           = 5000
    window            = 2
    initial           = 1
  }

  healthcheck {
    name              = "GUPAS-healthcheck"
    host              = terraform.workspace == "origin-staging" ? "gupas-staging.gannettdigital.com" : "gupas.gannettdigital.com"
    path              = "/status/"
    expected_response = 200
    method            = "GET"
    check_interval    = 60000
    threshold         = 1
    timeout           = 5000
    window            = 2
    initial           = 1
  }

  # UW endpoint used by the UW-Fastly LoadBalancing code
  # East - universal-web-east
  backend {
    address               = terraform.workspace == "origin-staging" ? "universal-web.staging.gannettdigital.com" : "universal-web-east.production.gannettdigital.com"
    name                  = "universal-web-east"
    shield                = "iad-va-us"
    healthcheck           = "universal-web-east-healthcheck"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = terraform.workspace == "origin-staging" ? "universal-web.staging.gannettdigital.com" : "universal-web-east.production.gannettdigital.com"
    ssl_cert_hostname     = "*.gannettdigital.com"
    request_condition     = "no_default_host"
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 1000
    auto_loadbalance      = false
  }

  healthcheck {
    name              = "universal-web-east-healthcheck"
    host              = terraform.workspace == "origin-staging" ? "universal-web.staging.gannettdigital.com" : "universal-web-east.production.gannettdigital.com"
    path              = "/status"
    expected_response = 200
    method            = "GET"
    check_interval    = 60000
    threshold         = 1
    timeout           = 5000
    window            = 2
    initial           = 1
  }
  # West - universal-web-west
  backend {
    address               = terraform.workspace == "origin-staging" ? "universal-web.staging.gannettdigital.com" : "universal-web-west.production.gannettdigital.com"
    name                  = "universal-web-west"
    shield                = "iad-va-us"
    healthcheck           = "universal-web-west-healthcheck"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = terraform.workspace == "origin-staging" ? "universal-web.staging.gannettdigital.com" : "universal-web-west.production.gannettdigital.com"
    ssl_cert_hostname     = "*.gannettdigital.com"
    request_condition     = "no_default_host"
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 1000
    auto_loadbalance      = false
  }

  healthcheck {
    name              = "universal-web-west-healthcheck"
    host              = terraform.workspace == "origin-staging" ? "universal-web.staging.gannettdigital.com" : "universal-web-west.production.gannettdigital.com"
    path              = "/status"
    expected_response = 200
    method            = "GET"
    check_interval    = 60000
    threshold         = 1
    timeout           = 5000
    window            = 2
    initial           = 1
  }

  # Storytelling Agent
  backend {
    address               = terraform.workspace == "origin-staging" ? "s2-agent.staging.gannettdigital.com" : "s2-agent-east.production.gannettdigital.com"
    name                  = "s2-agent-east"
    shield                = "iad-va-us"
    healthcheck           = "s2-agent-east-healthcheck"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = terraform.workspace == "origin-staging" ? "s2-agent.staging.gannettdigital.com" : "s2-agent-east.production.gannettdigital.com"
    ssl_cert_hostname     = "*.gannettdigital.com"
    request_condition     = "no_default_host"
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 1000
    auto_loadbalance      = false
  }

  backend {
    address               = terraform.workspace == "origin-staging" ? "s2-agent.staging.gannettdigital.com" : "s2-agent-west.production.gannettdigital.com"
    name                  = "s2-agent-west"
    shield                = "iad-va-us"
    healthcheck           = "s2-agent-west-healthcheck"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = terraform.workspace == "origin-staging" ? "s2-agent.staging.gannettdigital.com" : "s2-agent-west.production.gannettdigital.com"
    ssl_cert_hostname     = "*.gannettdigital.com"
    request_condition     = "no_default_host"
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 1000
    auto_loadbalance      = false
  }

  healthcheck {
    name              = "s2-agent-east-healthcheck"
    host              = terraform.workspace == "origin-staging" ? "s2-agent.staging.gannettdigital.com" : "s2-agent-east.production.gannettdigital.com"
    path              = "/"
    expected_response = 200
    method            = "GET"
    check_interval    = 60000
    threshold         = 1
    timeout           = 5000
    window            = 2
    initial           = 1
  }

  healthcheck {
    name              = "s2-agent-west-healthcheck"
    host              = terraform.workspace == "origin-staging" ? "s2-agent.staging.gannettdigital.com" : "s2-agent-west.production.gannettdigital.com"
    path              = "/"
    expected_response = 200
    method            = "GET"
    check_interval    = 60000
    threshold         = 1
    timeout           = 5000
    window            = 2
    initial           = 1
  }

  # TANGENT
  backend {
    address               = terraform.workspace == "origin-staging" ? "tangent.staging.gannettdigital.com" : "tangent-east.production.gannettdigital.com"
    name                  = "tangent-east"
    healthcheck           = "tangent-east-healthcheck"
    shield                = "iad-va-us"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = terraform.workspace == "origin-staging" ? "tangent.staging.gannettdigital.com" : "tangent-east.production.gannettdigital.com"
    ssl_cert_hostname     = terraform.workspace == "origin-staging" ? "tangent.staging.gannettdigital.com" : "tangent-east.production.gannettdigital.com"
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 6000
  }

  backend {
    address               = terraform.workspace == "origin-staging" ? "tangent.staging.gannettdigital.com" : "tangent-west.production.gannettdigital.com"
    name                  = "tangent-west"
    healthcheck           = "tangent-west-healthcheck"
    shield                = "iad-va-us"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = terraform.workspace == "origin-staging" ? "tangent.staging.gannettdigital.com" : "tangent-west.production.gannettdigital.com"
    ssl_cert_hostname     = terraform.workspace == "origin-staging" ? "tangent.staging.gannettdigital.com" : "tangent-west.production.gannettdigital.com"
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 6000
  }

  backend {
    address               = terraform.workspace == "origin-staging" ? "tangent-fragments.staging.gannettdigital.com" : "tangent-fragments-east.production.gannettdigital.com"
    name                  = "tangent-fragments-east"
    healthcheck           = "tangent-fragments-east-healthcheck"
    shield                = "iad-va-us"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = terraform.workspace == "origin-staging" ? "tangent-fragments.staging.gannettdigital.com" : "tangent-fragments-east.production.gannettdigital.com"
    ssl_cert_hostname     = terraform.workspace == "origin-staging" ? "tangent-fragments.staging.gannettdigital.com" : "tangent-fragments-east.production.gannettdigital.com"
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 6000
  }

  backend {
    address               = terraform.workspace == "origin-staging" ? "tangent-fragments.staging.gannettdigital.com" : "tangent-fragments-west.production.gannettdigital.com"
    name                  = "tangent-fragments-west"
    healthcheck           = "tangent-fragments-west-healthcheck"
    shield                = "iad-va-us"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = terraform.workspace == "origin-staging" ? "tangent-fragments.staging.gannettdigital.com" : "tangent-fragments-west.production.gannettdigital.com"
    ssl_cert_hostname     = terraform.workspace == "origin-staging" ? "tangent-fragments.staging.gannettdigital.com" : "tangent-fragments-west.production.gannettdigital.com"
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 6000
  }
  # reommendation prediction service Backend - #NO SHIELD
  backend {
    address               = terraform.workspace == "origin-staging" ? "staging-rec-ai-compute.usatoday.com" : "rec-ai-compute.usatoday.com"
    name                  = "recommendation-prediction-service"
    shield                = "iad-va-us"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = terraform.workspace == "origin-staging" ? "staging-rec-ai-compute.usatoday.com" : "rec-ai-compute.usatoday.com"
    ssl_cert_hostname     = terraform.workspace == "origin-staging" ? "staging-rec-ai-compute.usatoday.com" : "rec-ai-compute.usatoday.com"
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 6000
  }

  backend {
    address           = "www.gannett-cdn.com"
    name              = "www.gannett-cdn.com"
    shield            = "iad-va-us"
    port              = 443
    use_ssl           = true
    ssl_check_cert    = true
    ssl_sni_hostname  = "www.gannett-cdn.com"
    ssl_cert_hostname = "www.gannett-cdn.com"
    request_condition = "no_default_host"
    auto_loadbalance  = false
  }
  # GCDN Backend - #NO SHIELD
  backend {
    address           = "www.gannett-cdn.com"
    name              = "noshield-www.gannett-cdn.com"
    shield            = "iad-va-us"
    port              = 443
    use_ssl           = true
    ssl_check_cert    = true
    ssl_sni_hostname  = "www.gannett-cdn.com"
    ssl_cert_hostname = "www.gannett-cdn.com"
    request_condition = "no_default_host"
    auto_loadbalance  = false
  }

  backend {
    address               = "thewall-production.usatoday.com"
    name                  = "thewall-production.usatoday.com"
    shield                = "iad-va-us"
    healthcheck           = "thewall-production.usatoday.com"
    port                  = 443
    ssl_cert_hostname     = "thewall-production.usatoday.com"
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    use_ssl               = true
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 1000
  }

  backend {
    address           = "media.gannett-cdn.com"
    name              = "chain_media.gannett-cdn.com"
    shield            = "iad-va-us"
    port              = 443
    ssl_cert_hostname = "media.gannett-cdn.com"
    ssl_sni_hostname  = "media.gannett-cdn.com"
    request_condition = "no_default_host"
    auto_loadbalance  = false
    use_ssl           = true
  }

  backend {
    address               = "storage.googleapis.com"
    name                  = "storage.googleapis.com"
    shield                = "iad-va-us"
    port                  = 443
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    ssl_cert_hostname     = "storage.googleapis.com"
    ssl_sni_hostname      = "storage.googleapis.com"
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 6000
    use_ssl               = true
  }

  backend {
    address           = "newstips.usatoday.com"
    name              = "newstips.usatoday.com"
    shield            = "iad-va-us"
    port              = 443
    ssl_cert_hostname = "newstips.usatoday.com"
    request_condition = "no_default_host"
    auto_loadbalance  = false
    use_ssl           = true
  }

  backend {
    address               = "sportsbet.usatoday.com"
    name                  = "chain_sportsbet.usatoday.com"
    shield                = "iad-va-us"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = "*.usatoday.com"
    ssl_cert_hostname     = "*.usatoday.com"
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 6000
  }

  backend {
    address               = "origin-east-notable-deaths.usatoday.com"
    name                  = "celebrity-deaths-east.usatoday.com"
    shield                = "iad-va-us"
    healthcheck           = "celebrity-deaths-east.usatoday.com"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = "origin-east-notable-deaths.usatoday.com"
    ssl_cert_hostname     = "origin-east-notable-deaths.usatoday.com"
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 6000
  }

  backend {
    address               = "origin-west-notable-deaths.usatoday.com"
    name                  = "celebrity-deaths-west.usatoday.com"
    shield                = "iad-va-us"
    healthcheck           = "celebrity-deaths-west.usatoday.com"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = "origin-west-notable-deaths.usatoday.com"
    ssl_cert_hostname     = "origin-west-notable-deaths.usatoday.com"
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 6000
  }

  backend {
    address               = terraform.workspace == "origin-staging" ? "dev-origin-most-popular.usatoday.com" : "origin-east-most-popular.usatoday.com"
    name                  = "most-popular-east.usatoday.com"
    shield                = "iad-va-us"
    healthcheck           = "most-popular-east.usatoday.com"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = terraform.workspace == "origin-staging" ? "dev-origin-most-popular.usatoday.com" : "origin-east-most-popular.usatoday.com"
    ssl_cert_hostname     = terraform.workspace == "origin-staging" ? "dev-origin-most-popular.usatoday.com" : "origin-east-most-popular.usatoday.com"
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 6000
  }

  backend {
    address               = terraform.workspace == "origin-staging" ? "dev-origin-most-popular.usatoday.com" : "origin-west-most-popular.usatoday.com"
    name                  = "most-popular-west.usatoday.com"
    shield                = "iad-va-us"
    healthcheck           = "most-popular-west.usatoday.com"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = terraform.workspace == "origin-staging" ? "dev-origin-most-popular.usatoday.com" : "origin-west-most-popular.usatoday.com"
    ssl_cert_hostname     = terraform.workspace == "origin-staging" ? "dev-origin-most-popular.usatoday.com" : "origin-west-most-popular.usatoday.com"
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 6000
  }
  # www.usatoday-gdcgroup.com Backend - #NO SHIELD
  backend {
    address               = "www.usatoday-gdcgroup.com"
    name                  = "www.usatoday-gdcgroup.com"
    shield                = "iad-va-us"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = "*.usatoday-gdcgroup.com"
    ssl_cert_hostname     = "*.usatoday-gdcgroup.com"
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 6000
  }
  # usat2.usatoday-gdcgroup Backend - #NO SHIELD
  backend {
    address               = "usat2.usatoday-gdcgroup.com"
    name                  = "usat2.usatoday-gdcgroup.com"
    shield                = "iad-va-us"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = "*.usatoday-gdcgroup.com"
    ssl_cert_hostname     = "*.usatoday-gdcgroup.com"
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 6000
  }

  healthcheck {
    name              = "booklist-east-healthcheck"
    host              = terraform.workspace == "origin-staging" ? "booklist-us-east1-7tpppv3ona-ue.a.run.app" : "booklist-us-east1-suh2mjo3oa-ue.a.run.app"
    path              = "/booklist/api/serviceStatus"
    expected_response = 200
    method            = "GET"
    check_interval    = 60000
    threshold         = 1
    timeout           = 5000
    window            = 2
    initial           = 1
  }

  healthcheck {
    name              = "booklist-west-healthcheck"
    host              = terraform.workspace == "origin-staging" ? "booklist-us-east1-7tpppv3ona-ue.a.run.app" : "booklist-us-west1-suh2mjo3oa-uw.a.run.app"
    path              = "/booklist/api/serviceStatus"
    expected_response = 200
    method            = "GET"
    check_interval    = 60000
    threshold         = 1
    timeout           = 5000
    window            = 2
    initial           = 1
  }

  healthcheck {
    name              = "sportscontests-east-healthcheck"
    host              = terraform.workspace == "origin-staging" ? "dx-games-live-us-east1-45898334026.us-east1.run.app" : "dx-games-live-us-east1-664912083968.us-east1.run.app"
    path              = "/sports/contests/api/serviceStatus"
    expected_response = 200
    method            = "GET"
    check_interval    = 60000
    threshold         = 1
    timeout           = 5000
    window            = 2
    initial           = 1
  }

  healthcheck {
    name              = "sportscontests-west-healthcheck"
    host              = terraform.workspace == "origin-staging" ? "dx-games-live-us-east1-45898334026.us-east1.run.app" : "dx-games-live-us-west1-664912083968.us-west1.run.app"
    path              = "/sports/contests/api/serviceStatus"
    expected_response = 200
    method            = "GET"
    check_interval    = 60000
    threshold         = 1
    timeout           = 5000
    window            = 2
    initial           = 1
  }

  healthcheck {
    name              = "most-popular-east.usatoday.com"
    host              = terraform.workspace == "origin-staging" ? "dev-origin-most-popular.usatoday.com" : "origin-east-most-popular.usatoday.com"
    path              = "/story-of-the-day/health-check"
    expected_response = 200
    method            = "GET"
    check_interval    = 60000
    threshold         = 1
    timeout           = 5000
    window            = 2
    initial           = 1
  }

  healthcheck {
    name              = "most-popular-west.usatoday.com"
    host              = terraform.workspace == "origin-staging" ? "dev-origin-most-popular.usatoday.com" : "origin-west-most-popular.usatoday.com"
    path              = "/story-of-the-day/health-check"
    expected_response = 200
    method            = "GET"
    check_interval    = 60000
    threshold         = 1
    timeout           = 5000
    window            = 2
    initial           = 1
  }

  healthcheck {
    name              = "celebrity-deaths-east.usatoday.com"
    host              = "origin-east-notable-deaths.usatoday.com"
    path              = "/celebrity-deaths/health-check"
    expected_response = 200
    method            = "GET"
    check_interval    = 60000
    threshold         = 1
    timeout           = 5000
    window            = 2
    initial           = 1
  }
  healthcheck {
    name              = "celebrity-deaths-west.usatoday.com"
    host              = "origin-west-notable-deaths.usatoday.com"
    path              = "/celebrity-deaths/health-check"
    expected_response = 200
    method            = "GET"
    check_interval    = 60000
    threshold         = 1
    timeout           = 5000
    window            = 2
    initial           = 1
  }

  healthcheck {
    name              = "vip-lb.wordpress.com"
    host              = "sportspolls.usatoday.com"
    path              = "/osd.xml"
    expected_response = 200
    method            = "GET"
    check_interval    = 60000
    threshold         = 1
    timeout           = 5000
    window            = 2
    initial           = 1
  }

  healthcheck {
    name              = "tangent-east-healthcheck"
    host              = terraform.workspace == "origin-staging" ? "tangent.staging.gannettdigital.com" : "tangent-east.production.gannettdigital.com"
    path              = "/_health/liveness/"
    expected_response = 200
    method            = "GET"
    check_interval    = 60000
    threshold         = 1
    timeout           = 5000
    window            = 2
    initial           = 1
  }

  healthcheck {
    name              = "tangent-west-healthcheck"
    host              = terraform.workspace == "origin-staging" ? "tangent.staging.gannettdigital.com" : "tangent-west.production.gannettdigital.com"
    path              = "/_health/liveness/"
    expected_response = 200
    method            = "GET"
    check_interval    = 60000
    threshold         = 1
    timeout           = 5000
    window            = 2
    initial           = 1
  }

  healthcheck {
    name              = "tangent-fragments-east-healthcheck"
    host              = terraform.workspace == "origin-staging" ? "tangent-fragments.staging.gannettdigital.com" : "tangent-fragments-east.production.gannettdigital.com"
    path              = "/_health/liveness/"
    expected_response = 200
    method            = "GET"
    check_interval    = 60000
    threshold         = 1
    timeout           = 5000
    window            = 2
    initial           = 1
  }

  healthcheck {
    name              = "tangent-fragments-west-healthcheck"
    host              = terraform.workspace == "origin-staging" ? "tangent-fragments.staging.gannettdigital.com" : "tangent-fragments-west.production.gannettdigital.com"
    path              = "/_health/liveness/"
    expected_response = 200
    method            = "GET"
    check_interval    = 60000
    threshold         = 1
    timeout           = 5000
    window            = 2
    initial           = 1
  }

  healthcheck {
    name              = "thewall-production.usatoday.com"
    host              = "thewall-production.usatoday.com"
    path              = "/version"
    expected_response = 200
    method            = "GET"
    check_interval    = 60000
    threshold         = 1
    timeout           = 5000
    window            = 2
    initial           = 1
  }

  healthcheck {
    name              = "newstips.usatoday.com"
    host              = "usatoday.com"
    path              = "/newstips"
    expected_response = 200
    method            = "GET"
    check_interval    = 60000
    threshold         = 1
    timeout           = 5000
    window            = 2
    initial           = 1
  }

  # prebid Backend - #NO SHIELD
  backend {
    address               = "prebid-vcl.gannettdigital.com"
    name                  = "prebid-vcl"
    shield                = "iad-va-us"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = "*.gannettdigital.com"
    ssl_cert_hostname     = "*.gannettdigital.com"
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 6000
  }
  # prebid ci Backend - #NO SHIELD
  backend {
    address               = "ci-prebid-vcl.gannettdigital.com"
    name                  = "ci-prebid-vcl"
    shield                = "iad-va-us"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = "*.gannettdigital.com"
    ssl_cert_hostname     = "*.gannettdigital.com"
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 6000
  }

  # for public-notices app
  backend {
    address               = terraform.workspace == "origin-staging" ? "public-notices-web-cdn-preprod.gannettdigital.com" : "public-notices-web-cdn-prod.gannettdigital.com"
    name                  = "public-notices"
    shield                = "iad-va-us"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = "*.gannettdigital.com"
    ssl_cert_hostname     = "*.gannettdigital.com"
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 6000
  }

  # vendor reverse-proxy Backend - #NO SHIELD
  backend {
    address               = "reverse-proxy-vcl.gannettdigital.com"
    name                  = "reverse-proxy-vcl"
    shield                = "iad-va-us"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = "*.gannettdigital.com"
    ssl_cert_hostname     = "*.gannettdigital.com"
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 6000
  }
  # vendor reverse-proxy ci Backend - #NO SHIELD
  backend {
    address               = "ci-reverse-proxy-vcl.gannettdigital.com"
    name                  = "reverse-proxy-vcl-ci"
    shield                = "iad-va-us"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = "*.gannettdigital.com"
    ssl_cert_hostname     = "*.gannettdigital.com"
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 6000
  }

  backend {
    # /election Cloud Run East backend
    address               = terraform.workspace == "origin-staging" ? "elections-us-east1-7tpppv3ona-ue.a.run.app" : "elections-us-east1-suh2mjo3oa-ue.a.run.app"
    name                  = "elections-east"
    shield                = "iad-va-us"
    healthcheck           = "elections-east-healthcheck"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = "*.usatoday.com"
    ssl_cert_hostname     = "*.a.run.app"
    request_condition     = "no_default_host"
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 8000
    auto_loadbalance      = false
  }

  backend {
    # /election Cloud Run West backend
    address               = terraform.workspace == "origin-staging" ? "elections-us-east1-7tpppv3ona-ue.a.run.app" : "elections-us-west1-suh2mjo3oa-uw.a.run.app"
    name                  = "elections-west"
    shield                = "bfi-wa-us"
    healthcheck           = "elections-west-healthcheck"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = "*.usatoday.com"
    ssl_cert_hostname     = "*.a.run.app"
    request_condition     = "no_default_host"
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 8000
    auto_loadbalance      = false
  }

  healthcheck {
    name              = "elections-east-healthcheck"
    host              = terraform.workspace == "origin-staging" ? "elections-us-east1-7tpppv3ona-ue.a.run.app" : "elections-us-east1-suh2mjo3oa-ue.a.run.app"
    path              = "/elections/api/serviceStatus"
    expected_response = 200
    method            = "GET"
    check_interval    = 60000
    threshold         = 1
    timeout           = 5000
    window            = 2
    initial           = 1
  }

  healthcheck {
    name              = "elections-west-healthcheck"
    host              = terraform.workspace == "origin-staging" ? "elections-us-east1-7tpppv3ona-ue.a.run.app" : "elections-us-west1-suh2mjo3oa-uw.a.run.app"
    path              = "/elections/api/serviceStatus"
    expected_response = 200
    method            = "GET"
    check_interval    = 60000
    threshold         = 1
    timeout           = 5000
    window            = 2
    initial           = 1
  }

  backend {
    # /election Cloud Run East backend
    address               = "xpr-gannett.com"
    name                  = "xpr-gannett.com"
    shield                = "iad-va-us"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_cert_hostname     = "*.xpr-gannett.com"
    request_condition     = "no_default_host"
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 8000
    auto_loadbalance      = false
  }

  snippet {
    name     = "disable-shield"
    type     = "recv"
    priority = 10
    content  = <<-EOT
              if (req.backend == F_retail_googleapis_com ||
              req.backend == F_gcias_compute_usatoday_com ||
              req.backend == F_staging_gcias_compute_usatoday_com ||
              req.backend == F_ci_gcias_compute_usatoday_com ||
              req.backend == F_prebid_vcl ||
              req.backend == F_ci_prebid_vcl ||
              req.backend == F_recommendation_prediction_service ||
              req.backend == F_noshield_www_gannett_cdn_com ||
              req.backend == F_www_usatoday_gdcgroup_com ||
              req.backend == F_usat2_usatoday_gdcgroup_com ||
              req.backend == F_reverse_proxy_vcl ||
              req.backend == F_reverse_proxy_vcl_ci
              ) {
                set var.fastly_req_do_shield = false;
          }
              EOT
  }

  # guard us from destroying production
  force_destroy = terraform.workspace == "production" ? false : true

  condition {
    name      = "Generated by IP block list"
    priority  = 0
    statement = "client.ip ~ Generated_by_IP_block_list"
    type      = "REQUEST"
  }
  response_object {
    content_type      = "text/html"
    name              = "Generated by IP block list"
    request_condition = "Generated by IP block list"
    response          = "Forbidden"
    status            = 403
  }

  logging_https {
    name         = "tollbit-prod"
    url          = "https://log.tollbit.com/log"
    header_name  = "TollbitKey"
    header_value = "e874c5d2bcba5879d016c9a6740a75d3f4d737de1a4b3549cb02bc037f8c2f0e"
  }

  logging_bigquery {
    name       = "bigquery-logs"
    dataset    = "sre_fastly_logs"
    project_id = "gannett-sre-monitoring"
    table      = terraform.workspace == "production" ? "sre_fastly_logs_usatoday" : "********************************"
    email      = var.FASTLY_MON_BQ_EMAIL
    secret_key = var.FASTLY_MON_BQ_SECRET_KEY
  }

  logging_bigquery {
    name       = "wally-logs"
    dataset    = "GUP_fastly_logs"
    project_id = "gannett-sre-monitoring"
    table      = terraform.workspace == "production" ? "wally_fastly_logs" : "wally_fastly_logs_staging"
    email      = var.FASTLY_MON_BQ_EMAIL
    secret_key = var.FASTLY_MON_BQ_SECRET_KEY
  }

  logging_gcs {
    name         = "fastly-log-bucket"
    bucket_name  = "fastly-logging-bucket"
    placement    = "none"
    message_type = "blank"
    period       = 60
    gzip_level   = 0
    user         = var.FASTLY_GCS_EMAIL
    secret_key   = var.FASTLY_GCS_SECRET_KEY
  }

  vcl {
    name    = "main"
    content = file("${path.module}/main.vcl")
    main    = true
  }

  vcl {
    name    = "wall-ly"
    content = file("${path.module}/lib/wall-ly/vcl/wall-ly.vcl")
    main    = false
  }

  vcl {
    name    = "wall-ly_crawler"
    content = file("${path.module}/lib/wall-ly/vcl/wally_allowed_crawler_ips.vcl")
    main    = false
  }

  vcl {
    name    = "guplib"
    content = file("${path.module}/lib/guplib/vcl/guplib.vcl")
    main    = false
  }

  vcl {
    name    = "abtest"
    content = file("${path.module}/abtest.vcl")
    main    = false
  }

  vcl {
    name    = "redirects"
    content = file("${path.module}/redirects.vcl")
    main    = false
  }

  vcl {
    name    = "helpers"
    content = file("${path.module}/helpers.vcl")
    main    = false
  }

  vcl {
    name    = "buildinfo"
    content = file("${path.module}/buildinfo.vcl")
    main    = false
  }

  vcl {
    name    = "tables"
    content = file("${path.module}/tables.vcl")
    main    = false
  }


  vcl {
    name    = "sports-tables"
    content = file("${path.module}/sports-tables.vcl")
    main    = false
  }

  vcl {
    name    = "uw-whitelist"
    content = file("${path.module}/uw-whitelist.vcl")
    main    = false
  }

  vcl {
    name    = "storytelling-agent-paths"
    content = file("${path.module}/storytelling-agent-paths.vcl")
    main    = false
  }

  vcl {
    name    = "eu-experience"
    content = file("${path.module}/eu-experience.vcl")
    main    = false
  }

  vcl {
    name    = "sports"
    content = file("${path.module}/sports.vcl")
    main    = false
  }

  vcl {
    name    = "traffic-mitigation"
    content = file("${path.module}/traffic-mitigation.vcl")
    main    = false
  }

  vcl {
    name    = "tangent"
    content = file("${path.module}/tangent.vcl")
    main    = false
  }

  vcl {
    name    = "vendor-apps"
    content = file("${path.module}/vendor-apps.vcl")
    main    = false
  }

  vcl {
    name    = "elections"
    content = file("${path.module}/elections.vcl")
    main    = false
  }

  vcl {
    name    = "backend-healthcheck"
    content = file("${path.module}/backend-healthcheck.vcl")
    main    = false
  }

  vcl {
    name    = "rate-limit"
    content = file("${path.module}/rate-limit.vcl")
    main    = false
  }

  vcl {
    name    = "cam_disabled"
    content = file("${path.module}/cam_disabled.vcl")
    main    = false
  }

  product_enablement {
    image_optimizer = true
  }

  dynamicsnippet {
    name     = "host_redirect"
    type     = "none"
    priority = 10
  }

  dynamicsnippet {
    name     = "path_redirect_nonrelative_host"
    type     = "none"
    priority = 10
  }

  dynamicsnippet {
    name     = "path_redirect_offers"
    type     = "none"
    priority = 10
  }

  dynamicsnippet {
    name     = "path_redirect_relative_host"
    type     = "none"
    priority = 10
  }

  dynamicsnippet {
    name     = "shared_critical"
    type     = "none"
    priority = 10
  }

  dynamicsnippet {
    name     = "shared_tangent"
    type     = "none"
    priority = 10
  }

  dynamicsnippet {
    name     = "shared_helpers_general"
    type     = "none"
    priority = 10
  }

  dynamicsnippet {
    name     = "shared_prebid"
    type     = "none"
    priority = 10
  }

  dynamicsnippet {
    name     = "shared_eu"
    type     = "none"
    priority = 10
  }

  dynamicsnippet {
    name     = "shared_ab_testing"
    type     = "none"
    priority = 10
  }

  dynamicsnippet {
    name     = "shared_proxy_gannett"
    type     = "none"
    priority = 10
  }

  dynamicsnippet {
    name     = "shared_proxy_vendor"
    type     = "none"
    priority = 10
  }

  dynamicsnippet {
    name     = "shared_helpers_frontend"
    type     = "none"
    priority = 10
  }

  dynamicsnippet {
    name     = "shared_detection"
    type     = "none"
    priority = 10
  }

  ###############################################
  ####################-NGWAF-####################
  ###############################################

  #### NGWAF Dynamic Snippets - MANAGED BY FASTLY - Start
  dynamicsnippet {
    name     = "ngwaf_config_init"
    type     = "init"
    priority = 0
  }
  dynamicsnippet {
    name     = "ngwaf_config_miss"
    type     = "miss"
    priority = 9000
  }
  dynamicsnippet {
    name     = "ngwaf_config_pass"
    type     = "pass"
    priority = 9000
  }
  dynamicsnippet {
    name     = "ngwaf_config_deliver"
    type     = "deliver"
    priority = 9000
  }
  #### NGWAF Dynamic Snippets - MANAGED BY FASTLY - End

  dictionary {
    name = "Edge_Security"
  }

  dictionary {
    name = "tangent_concierge_search_redirect_usat"
  }

  dictionary {
    name = "tangent_recalls_category_whitelist"
  }

  dictionary {
    name = "tangent_asset_sponsored_blacklist"
  }

  dictionary {
    name = "tangent_asset_editorial_blacklist"
  }

  dictionary {
    name = "usat_amp_redirects"
  }

  dictionary {
    name = "clientip_mitigation"
  }

  dictionary {
    name = "continent_mitigation"
  }

  dictionary {
    name = "country_mitigation"
  }

  dictionary {
    name = "referrer_mitigation"
  }

  dictionary {
    name = "user_agent_mitigation"
  }

  dictionary {
    name = "weight_shift"
  }

  dictionary {
    name = "gnt_banner_data"
  }

  dictionary {
    name = "gnt_zip_east"
  }

  dictionary {
    name = "gnt_zip_west"
  }

  dictionary {
    name = "gnt_zip_sitedata"
  }

  dictionary {
    name = "gnt_weather_data"
  }

  dictionary {
    name = "gnt_weather_redirects"
  }

  dictionary {
    name = "gnt_weather_conditions_usat"
  }

  dictionary {
    name = "usat_cam_settings"
  }

  dictionary {
    name = "paths_to_410_usat"
  }

  dictionary {
    name = "sports_gaming_geo"
  }

  dictionary {
    name = "edge_auth"
  }

  dictionary {
    name = "rate_limit_values"
  }

  dictionary {
    name = "cam_disabled_ip"
  }

  dictionary {
    name = "logging_rate"
  }

  dictionary {
    name = "ai_bots"
  }

  # See https://github.com/GannettDigital/wall-ly#readme for info on the following wally_sso_* tables

  dictionary {
    name = "wally_sso_settings"
  }

  dictionary {
    name = "wally_sso_always_current_hostnames"
  }

  dictionary {
    name = "wally_sso_roadblock_check_hostnames"
  }

  dictionary {
    name       = "wally_sso_crypt_key"
    write_only = true
  }

  dictionary {
    name       = "wally_sso_crypt_iv"
    write_only = true
  }

  dictionary {
    name = "wally_dynamic_settings"
  }

  dictionary {
    name = "gnt_elections_redirects"
  }

  dictionary {
    name = "gnt_sportshub_back_button"
  }

  acl {
    name = "Generated_by_IP_block_list"
  }
}

output "service_id" {
  value = fastly_service_vcl.fastly.id
}

output "service_address" {
  value = terraform.workspace == "production" ? format("%s.global.prod.fastly.net", var.fastly_name) : format("%s-%s.global.prod.fastly.net", terraform.workspace, var.fastly_name)
}

output "active_version" {
  value = fastly_service_vcl.fastly.active_version
}
