#########################################
########### /sports/contests/ ###########
### Brackets + Survivor-Pool - DX App ###
#########################################

sub gnt_sportscontests_recv {
  call select_geo_specific_region;
  set req.http.Gannett-Custom:sportscontests = "1";

  if (req.url.path ~ "^/sports/contests/survivor-pool") {     #survivor-pool app
    set req.http.Gannett-Debug-Path-Item = "survivor-pool";
    call shared_helpers_general_record_object_path;
  } elseif (req.url.path ~ "^/sports/contests/brackets") {    # brackets app
    set req.http.Gannett-Debug-Path-Item = "brackets";
    call shared_helpers_general_record_object_path;
  } elseif (req.url.path == "/sports/contests") {             # Landing Page
    set req.http.Gannett-Debug-Path-Item = "sportscontests-page";
    call shared_helpers_general_record_object_path;
  }
  # keep old setting without force br
  call shared_helpers_frontend_keep_gzip_override;

  if(req.http.host ~ "^origin-staging-") {
    set req.http.x-origin-expected-host = "dx-games-live-us-east1-45898334026.us-east1.run.app";
  } else {
      if(req.backend == F_sportscontests_east) {
        set req.http.x-origin-expected-host = "dx-games-live-us-east1-664912083968.us-east1.run.app";
      }
      else {
        set req.http.x-origin-expected-host = "dx-games-live-us-west1-664912083968.us-west1.run.app";
      }
    }
}

sub gnt_sportscontests_backend {
  set req.http.Gannett-Debug-Path-Item = "SPORTSCONTESTS backend:set";
  call shared_helpers_general_record_object_path;
  set req.backend = F_sportscontests_east;

  # Geo Based Load Balancing
  call announce_region_selection;
  if (req.http.gnt-client:geo-region == "east") {
    set req.backend = F_sportscontests_east;
  } else {
    set req.backend = F_sportscontests_west;
  }
  # check that the backend is healthy
  if(req.backend == F_sportscontests_east && !req.backend.healthy) {
    set req.backend = F_sportscontests_west;
    set req.http.Gannett-Debug-Path-Item = "east unhealthy";
    call shared_helpers_general_record_object_path;
  } else if(req.backend == F_sportscontests_west && !req.backend.healthy) {
    set req.backend = F_sportscontests_east;
    set req.http.Gannett-Debug-Path-Item = "west unhealthy";
    call shared_helpers_general_record_object_path;
  }
}

sub gnt_sportscontests_sk_setting {
  # Set specific SKs based on the paths
  if (req.url.path ~ "^/sports/contests/api/data") {
    set req.http.x-append-surrogate = "sportscontests-api";
  } elseif(std.prefixof(beresp.http.Content-Type, "text/html")) {  # Cache the HTML pages on Fastly
      if (req.url.path ~ "^/sports/contests/survivor-pool") {
        set req.http.x-append-surrogate = "survivor-pool-pages";
      } elseif (req.url.path ~ "^/sports/contests/brackets") {
        set req.http.x-append-surrogate = "brackets-pages";
      } elseif (req.url.path == "/sports/contests") {
        set req.http.x-append-surrogate = "sportscontest-home";
      }
  } elseif (req.url.path ~ "^/sports/contests/(webfonts|_next|favicon)") {     # Specifically set browser cache rules for fonts
    set req.http.x-append-surrogate = "sportscontests-assets";
  }
  set req.http.x-append-surrogate = req.url.path " " req.http.x-append-surrogate " " "sportscontests-global";
}

sub gnt_sportscontests_fetch {
  # Set specific TTLs and SKs based on the paths
  if (req.url.path ~ "^/sports/contests/api/data") {
    set beresp.ttl = 60s;
  } elseif(std.prefixof(beresp.http.Content-Type, "text/html")) {  # Cache the HTML pages on Fastly
    set beresp.ttl = 120m;
  } elseif (req.url.path ~ "^/sports/contests/(webfonts|_next|favicon)") {     # Specifically set browser cache rules for fonts
    set beresp.http.Cache-Control = "public, max-age=31536000, immutable";
    set beresp.ttl = 30d;
    call keep_origin_cache_control;
  } else {
    set beresp.ttl = 60m;
  }

  # Set failover TTL in the case there is an SK overflow from CAPI. The presence of Surrogate-Control:max-agent means there was an overflow.
  if (beresp.http.Surrogate-Control ~ "max-age"){
    set beresp.http.Surrogate-Control = "max-age=15"; #override to 15 seconds
    set beresp.ttl = 15s;
  }

  # set security policy headers
  set beresp.http.Content-Security-Policy = "upgrade-insecure-requests;frame-ancestors 'none';object-src 'none'";
  set beresp.http.Content-Security-Policy-Report-Only = "script-src https: blob: 'unsafe-inline' 'unsafe-eval' 'self';base-uri 'self';report-uri https://reporting-api.gannettinnovation.com";
  set beresp.http.Feature-Policy = "camera 'none';display-capture 'none';geolocation 'none';microphone 'none';payment 'none';usb 'none';xr-spatial-tracking 'none'";
  set beresp.http.Permissions-Policy = "bluetooth=(),camera=(),display-capture=(),geolocation=(),hid=(),identity-credentials-get=(),local-fonts=(),microphone=(),midi=(),otp-credentials=(),payment=(),publickey-credentials-create=(),publickey-credentials-get=(),serial=(),usb=(),window-management=(),xr-spatial-tracking=()";
  set beresp.http.Referrer-Policy = "strict-origin-when-cross-origin";
  set beresp.http.X-Content-Type-Options = "nosniff";
  set beresp.http.X-Frame-Options = "deny";
  set beresp.http.X-XSS-Protection = "1; mode=block";
  set beresp.http.Cross-Origin-Resource-Policy = "same-origin";

  # Dont PASS the HTML page because we are caching it on Fastly
  if( !(std.prefixof(beresp.http.Content-Type, "text/html")) && req.url.path !~ "^/sports/contests/api/data " ) {
    if (beresp.http.Cache-Control ~ "(private|no-store|no-cache)") {
      return(pass);
    }
  }
}

sub gnt_sportscontests_hash {
  if (req.http.jwt-token) {
    set req.hash += req.http.jwt-token;
  }
  # Allow caching for POST requests to specific endpoints
  if (req.request == "POST") {
    if (req.url.path ~ "^/sports/contests/api/data" ) {
      set req.hash += req.body.base64;
      if (req.http.If-None-Match) {
        set req.hash += "If-None-Match";
        set req.hash += req.http.If-None-Match;
      }
      if (req.http.If-Match) {
        set req.hash += "If-Match";
        set req.hash += req.http.If-Match;
      }
    }
  }
}

sub gnt_sportscontests_deliver {
  if(std.prefixof(resp.http.Content-Type, "text/html") || req.url.path ~ "^/sports/contests/api/data") {
    set resp.http.Cache-Control = "private,no-cache";
  }
}

sub process_sportscontests {
  set req.http.Gannett-Debug-Path-Item = "survivor-pool-redirect";
  call shared_helpers_general_record_object_path;
  set req.http.x-Redir-Url = "https://" + req.http.host  + "/sports/contests" + req.url;
  error 701 req.http.x-Redir-Url;
}

#########################################
############## Sports Hubs ##############
#######  True Tandem Backend app  #######
#########################################

sub gnt_sports_hubs_recv {
  set req.http.Gannett-Custom:sportshubs = "";
  set req.http.Gannett-Debug-Path-Item = "Sports-Hubs";
  call shared_helpers_general_record_object_path;

  if(req.http.host ~ "^origin-staging-2") {
    set req.http.x-origin-expected-host = "origin-staging-2-sportshubs.usatoday.com";
  } else {
    set req.http.x-origin-expected-host = "sportshubs.usatoday.com";
  }
}

sub gnt_sports_hubs_backend {
  set req.http.Gannett-Debug-Path-Item = "Sports-Hubs-Service-Chain";
  call shared_helpers_general_record_object_path;

  # keep old setting without force br
  call shared_helpers_frontend_keep_gzip_override;

  set req.backend = F_sports_hubs_east;
}

sub gnt_sports_hubs_fetch {
  return(pass);
}

sub gnt_sports_hubs_deliver {
  if(std.prefixof(resp.http.Content-Type, "text/html")) {
    set resp.http.Cache-Control = "private,no-cache";
  }
}
