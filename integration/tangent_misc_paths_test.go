package integration

import (
	"net/http"
	"os"
	"strings"
	"testing"

	fastly "github.com/GannettDigital/go-fastly-tests"
)

func TestTangentMiscPaths(t *testing.T) {
	t.<PERSON>()

	var tests = []fastly.Test{
		// test tangent favicon.ico
		fastly.Header{
			Request: fastly.Request{
				Description: "should get the favicon.ico from Tangent backend",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/favicon.ico",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
			Headers: http.Header{
				"Gannett-Debug-Path": []string{
					"region: tangent_usatoday",
				},
				"Cache-Control": []string{
					"public",
					"max-age=",
					"stale-while-revalidate=",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		// apple touch icons
		fastly.Header{
			Request: fastly.Request{
				Description: "should get the apple touch icon from Tangent backend",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/apple-touch-icon-180x180.png",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
			Headers: http.Header{
				"Gannett-Debug-Path": []string{
					"region: tangent_usatoday",
				},
				"Cache-Control": []string{
					"public",
					"max-age=",
					"stale-while-revalidate=",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		// tangsvc tests
		//
		// td
		fastly.Header{
			Request: fastly.Request{
				Description: "should select Tangent backend and set Fastly TTL to 7 days (604800s) and client side cache-control: public, max-age=60",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/tangsvc/td/nba/4/",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
			Headers: http.Header{
				"Gannett-Debug-Path": []string{
					"ttl: 604800.000",
					"region: tangent_usatoday",
				},
				"Cache-Control": []string{
					"public, max-age=60",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "should serve 400 for unsupported /tangsvc/td/ route ",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/tangsvc/td/front-id/3/bogus",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status:    400,
			Headers:   http.Header{},
			MatchType: fastly.PartialMatch{},
		},
		// scoreboard
		fastly.Header{
			Request: fastly.Request{
				Description: "should select Tangent backend  and set Fastly TTL to 7 days (604800s) and client side cache-control: public, max-age=60",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/tangsvc/sb/NHL/",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
			Headers: http.Header{
				"Gannett-Debug-Path": []string{
					"ttl: 604800.000",
					"region: tangent_usatoday",
				},
				"Cache-Control": []string{
					"public, max-age=60",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "should serve 400 for unsupported /tangsvc/sb/ route ",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/tangsvc/sb/NFL/3/bogus",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status:    400,
			Headers:   http.Header{},
			MatchType: fastly.PartialMatch{},
		},
		// photogallery
		fastly.Status{
			Request: fastly.Request{
				Description: "should serve 400 Bad Request for tangsvc photogallery request with invalid: assetId",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/tangsvc/pg/xxx/",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: 400,
		},
		// entertainment
		fastly.Status{
			Request: fastly.Request{
				Description: "should serve 200 for Tangent entertainment pages",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/entertainment/celebrities/essentials/",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
		},
		fastly.Status{
			Request: fastly.Request{
				Description: "should serve 400 Bad Request for tangsvc photogallery request with invalid: qsp",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/tangsvc/pg/123456/?ids=1234,",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: 400,
		},
		fastly.Status{
			Request: fastly.Request{
				Description: "should serve 400 Bad Request for tangsvc photogallery request with excess URL components",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/tangsvc/pg/123456/xxx/",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: 400,
		},
		// rp
		fastly.Status{
			Request: fastly.Request{
				Description: "should serve 400 Bad Request for tangsvc rp request with excess URL components",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/tangsvc/rp/primarytag/secondarytag1_secondarytag2/bogus",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: 400,
		},
		fastly.Status{
			Request: fastly.Request{
				Description: "should serve 200 for tangsvc rp request meeting URL validation requirements",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/tangsvc/rp/96e8fb88-0235-47b5-ac5f-642c8f45b77c/0570041d-af26-45dc-bbba-3bff6c6603b3_0ab99998-497f-4d1a-ae17-32d38d3fd97f/",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: 200,
		},
		// SafeFrame
		fastly.Header{
			Request: fastly.Request{
				Description: "should select Tangent backend for supported request to usatodaynetworkservice.com",
				Scheme:      "https://",
				Host:        "www.usatodaynetworkservice.com",
				Path:        "/tangstatic/html/usat/sf.html",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
			Headers: http.Header{
				"Gannett-Debug-Path": []string{
					"region: tangent_usatodaynetworkservice",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Status{
			Request: fastly.Request{
				Description: "should serve 404 for unsupported request to usatodaynetworkservice.com",
				Scheme:      "https://",
				Host:        "www.usatodaynetworkservice.com",
				Path:        "/",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: 404,
		},
		fastly.Status{
			Request: fastly.Request{
				Description: "should serve 404 for unsupported request to usatodaynetworkservice.com/tangstatic/",
				Scheme:      "https://",
				Host:        "www.usatodaynetworkservice.com",
				Path:        "/tangstatic/blah/",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: 404,
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "should not override Tangent provided header values - except for Strict-Transport-Security",
				Scheme:      "https://",
				Host:        "www.usatodaynetworkservice.com",
				Path:        "/tangstatic/html/usat/sf.html",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
			Headers: http.Header{
				"Gannett-Debug-Path": []string{
					"region: tangent_usatodaynetworkservice",
				},
				"Content-Security-Policy": []string{
					"frame-ancestors", // partial header value
				},
				"Strict-Transport-Security": []string{
					"max-age=63072000",
					"includeSubDomains",
					"preload",
				},
				"Timing-Allow-Origin": []string{
					"*",
				},
				"Cache-Control": []string{
					"public, immutable, max-age=315360000",
				},
				"X-Xss-Protection": []string{
					"1; mode=block",
				},
				"X-Content-Type-Options": []string{
					"nosniff",
				},
				"Feature-Policy": []string{
					";", // partial header value
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		// worker files
		fastly.Header{
			Request: fastly.Request{
				Description: "should select tangent as the backend for worker file enabled sites for sw.js",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sw.js",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
			Headers: http.Header{
				"Gannett-Debug-Path": []string{
					"region: tangent_usatoday",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "should select tangent as the backend for worker file enabled sites for push-worker.js",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/push-worker.js",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
			Headers: http.Header{
				"Gannett-Debug-Path": []string{
					"region: tangent_usatoday",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		// assetlinks.json
		fastly.Header{
			Request: fastly.Request{
				Description: "should select tangent as the backend for /.well-known/assetlinks.json",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/.well-known/assetlinks.json",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
			Headers: http.Header{
				"Gannett-Debug-Path": []string{
					"region: tangent_usatoday",
				},
				"Surrogate-Key": []string{
					"tangstatic_assetlinks",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		// ads.txt
		fastly.Header{
			Request: fastly.Request{
				Description: "should select tangent as the backend for /ads.txt",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/ads.txt",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
			Headers: http.Header{
				"Gannett-Debug-Path": []string{
					"region: tangent_usatoday",
				},
				"Surrogate-Key": []string{
					"tangstatic_ads",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		// app-ads.txt
		fastly.Header{
			Request: fastly.Request{
				Description: "should select tangent as the backend for /app-ads.txt",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/app-ads.txt",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
			Headers: http.Header{
				"Gannett-Debug-Path": []string{
					"region: tangent_usatoday",
				},
				"Surrogate-Key": []string{
					"tangstatic_ads",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		// AASA
		fastly.Header{
			Request: fastly.Request{
				Description: "should select tangent as the backend for /.well-known/apple-app-site-association",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/.well-known/apple-app-site-association",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
			Headers: http.Header{
				"Gannett-Debug-Path": []string{
					"region: tangent_usatoday",
				},
				"Surrogate-Key": []string{
					"tangstatic_aasa",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		// /interactives/sponsor-story/
		fastly.Status{
			Request: fastly.Request{
				Description: "should serve a 403 status for a request to /interactives/sponsor-story/ with an unsupported browser user agent",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/interactives/sponsor-story/",
				UA:          "Mozilla/4.0 (compatible; MSIE 6.0; AOL 9.0; Windows 98)",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: 403,
		},
		// tangfrag-metadata.json
		fastly.Status{
			Request: fastly.Request{
				Description: "should return a 401 when Basic Auth not set correctly for /tangfrag-metadata.json",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/tangfrag-metadata.json",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers: http.Header{
					"Authorization": []string{
						"Basic d",
					},
				},
			},
			Status: 401,
		},
		fastly.Status{
			Request: fastly.Request{
				Description: "should return a 200 for /tangfrag-metadata.json with basic auth",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/tangfrag-metadata.json",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers: http.Header{
					"Authorization": []string{
						"Basic dGFuZ2ZyYWc6ODJnc2x0JjJiNCFm",
					},
				},
			},
			Status: http.StatusOK,
		},
		// xrpvr
		fastly.Header{
			Request: fastly.Request{
				Description: "for /xrpvr/shopper.flipp.com/* - should PASS to reverse-proxy-vcl chained service and have proper resp headers set",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/xrpvr/shopper.flipp.com/tag/js/flipptag.js",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
			Headers: http.Header{
				"X-Content-Type-Options": []string{
					"nosniff",
				},
				"Cache-Control": []string{
					"public, max-age=900",
				},
				"Server-Timing": []string{
					"PASS",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Status{
			Request: fastly.Request{
				Description: "should serve 404 when resource is not available on a supported xrpvr backend",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/xrpvr/www.blah.com/some_resource.jpg",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusNotFound,
		},
	}

	var env = os.Getenv("ENVIRONMENT")
	for _, test := range tests {
		if strings.Contains(env, "origin-staging") {
			continue
		}
		test.Execute(t)
	}
}
