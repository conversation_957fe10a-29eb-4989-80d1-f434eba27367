  # 0 = unhealthy, 1 = healthy
  ##  This list can be generated like so:
  ###  grep "set req.backend" * | sort -u |  cut -d \; -f1 |awk '{print "\{\" \""$5"\": \"\} backend."$5".healthy \{\",\"\} LF"}' | sort -u
  ##
  sub backend_healthchecks {
  set obj.status = 200;
  set obj.response = "OK";
  set obj.http.content-type = "application/json";
  set obj.http.Cache-Control = "max-age=10";
  synthetic "{" LF
    {"  "timestamp": ""} now {"","} LF
    {" "F_blueprint_backend": "} backend.F_blueprint_backend.healthy {","} LF
    {" "F_booklist_east": "} backend.F_booklist_east.healthy {","} LF
    {" "F_booklist_west": "} backend.F_booklist_west.healthy {","} LF
    {" "F_celebrity_deaths_east_usatoday_com": "} backend.F_celebrity_deaths_east_usatoday_com.healthy {","} LF
    {" "F_celebrity_deaths_west_usatoday_com": "} backend.F_celebrity_deaths_west_usatoday_com.healthy {","} LF
    {" "F_elections_east": "} backend.F_elections_east.healthy {","} LF
    {" "F_elections_west": "} backend.F_elections_west.healthy {","} LF
    {" "F_gaming_chalkline": "} backend.F_gaming_chalkline.healthy {","} LF
    {" "F_holidaymarketplace_east": "} backend.F_holidaymarketplace_east.healthy {","} LF
    {" "F_holidaymarketplace_west": "} backend.F_holidaymarketplace_west.healthy {","} LF
    {" "F_petshub_east": "} backend.F_petshub_east.healthy {","} LF
    {" "F_petshub_west": "} backend.F_petshub_west.healthy {","} LF
    {" "F_lottery_east": "} backend.F_lottery_east.healthy {","} LF
    {" "F_lottery_west": "} backend.F_lottery_west.healthy {","} LF
    {" "F_chain_media_gannett_cdn_com": "} backend.F_chain_media_gannett_cdn_com.healthy {","} LF
    {" "F_most_popular_east_usatoday_com": "} backend.F_most_popular_east_usatoday_com.healthy {","} LF
    {" "F_most_popular_west_usatoday_com": "} backend.F_most_popular_west_usatoday_com.healthy {","} LF
    {" "F_newstips_usatoday_com": "} backend.F_newstips_usatoday_com.healthy {","} LF
    {" "F_s2_agent_east": "} backend.F_s2_agent_east.healthy {","} LF
    {" "F_s2_agent_west": "} backend.F_s2_agent_west.healthy {","} LF
    {" "F_chain_sportsbet_usatoday_com": "} backend.F_chain_sportsbet_usatoday_com.healthy {","} LF
    {" "F_storage_googleapis_com": "} backend.F_storage_googleapis_com.healthy {","} LF
    {" "F_sportscontests_east": "} backend.F_sportscontests_east.healthy {","} LF
    {" "F_sportscontests_west": "} backend.F_sportscontests_west.healthy {","} LF
    {" "F_tailgating_east": "} backend.F_tailgating_east.healthy {","} LF
    {" "F_tailgating_west": "} backend.F_tailgating_west.healthy {","} LF
    {" "F_tangent_east": "} backend.F_tangent_east.healthy {","} LF
    {" "F_tangent_fragments_east": "} backend.F_tangent_fragments_east.healthy {","} LF
    {" "F_tangent_fragments_west": "} backend.F_tangent_fragments_west.healthy {","} LF
    {" "F_tangent_west": "} backend.F_tangent_west.healthy {","} LF
    {" "F_thewall_production_usatoday_com": "} backend.F_thewall_production_usatoday_com.healthy {","} LF
    {" "F_universal_web_east": "} backend.F_universal_web_east.healthy {","} LF
    {" "F_universal_web_west": "} backend.F_universal_web_west.healthy {","} LF
    {" "F_vacation_east": "} backend.F_vacation_east.healthy {","} LF
    {" "F_vacation_west": "} backend.F_vacation_west.healthy {","} LF
    {" "F_vip_lb_wordpress_com": "} backend.F_vip_lb_wordpress_com.healthy {","} LF
    {" "F_woty_east": "} backend.F_woty_east.healthy {","} LF
    {" "F_woty_west": "} backend.F_woty_west.healthy {","} LF
    {" "F_www_gannett_cdn_com": "} backend.F_www_gannett_cdn_com.healthy {","} LF
    {" "F_www_usatoday_gdcgroup_com": "} backend.F_www_usatoday_gdcgroup_com.healthy LF
    "}";
  return (deliver);
}
