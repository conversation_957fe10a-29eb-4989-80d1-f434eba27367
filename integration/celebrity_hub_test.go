package integration

import (
	"net/http"
	"testing"

	fastly "github.com/GannettDigital/go-fastly-tests"
)

func TestCelebrityHub(t *testing.T) {
	t.<PERSON>()
	var tests = []fastly.Test{
		// celebrityhub endpoints
		fastly.Header{
			Request: fastly.Request{
				Description: "celebrityhub gets a 200 status",
				Method:      "GET",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/entertainment/celebrities/jenna-ortega",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
			Headers: http.Header{
				"Gannett-Debug-Path": []string{
					"celebrityhub backend:set",
				},
				"Surrogate-Key": []string{
					"celebrityhub",
				},
			},
			MatchType: fastly.PartialMatch{},
		},

		fastly.Header{
			Request: fastly.Request{
				Description: "celebrityhub music  gets a 200 status",
				Method:      "GET",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/entertainment/music/eminem",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
			Headers: http.Header{
				"Gannett-Debug-Path": []string{
					"celebrityhub backend:set",
				},
				"Surrogate-Key": []string{
					"celebrityhub",
				},
			},
			MatchType: fastly.PartialMatch{},
		},

		fastly.Header{
			Request: fastly.Request{
				Description: "Just celebrities/ gets a 200 tangent status",
				Method:      "GET",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/entertainment/celebrities/",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
			Headers: http.Header{
				"Gannett-Debug-Path": []string{
					"tangent_usatoday",
				},
				"Surrogate-Key": []string{
					"tangent_nf_USAT_entertainment",
				},
			},
			MatchType: fastly.PartialMatch{},
		},

		fastly.Header{
			Request: fastly.Request{
				Description: "Just music/ gets a 200 tangent status",
				Method:      "GET",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/entertainment/music/",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
			Headers: http.Header{
				"Gannett-Debug-Path": []string{
					"tangent_usatoday",
				},
				"Surrogate-Key": []string{
					"tangent_nf_USAT_entertainment",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
	}

	for _, test := range tests {
		test.Execute(t)
	}
}
