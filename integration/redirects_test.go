package integration

import (
	"net/http"
	"testing"

	fastly "github.com/GannettDigital/go-fastly-tests"
)

func TestRedirects(t *testing.T) {
	t.<PERSON>()
	var tests = []fastly.Test{
		fastly.Status{
			Request: fastly.Request{
				Description: "serve 404 for .htm .aspx and .ashx files",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/foo.htm",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusNotFound,
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "puzzles/crossword/ 301 to puzzles.usatoday.com with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/puzzles/crossword/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"puzzles.usatoday.com/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/fly 301 to promotions.usatoday.com/AA with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/fly",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"promotions.usatoday.com/AA",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/mobile 301 to / with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/mobile",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/interactives/tv-on-the-web 301 to /life/tv/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/interactives/tv-on-the-web",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/life/tv/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/interactives/tv-on-the-web/ 301 to /life/tv/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/interactives/tv-on-the-web/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/life/tv/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/life/tv-on-the-web 301 to /life/tv/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/life/tv-on-the-web",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/life/tv/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/tv/web-to-watch 301 to /life/tv/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/tv/web-to-watch",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/life/tv/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/tv/web-to-watch/ 301 to /life/tv/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/tv/web-to-watch/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/life/tv/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/life/web-to-watch/ 301 to /life/tv/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/life/web-to-watch/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/life/tv/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/yourtake/ 301 to /pages/interactives/your-take/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/yourtake/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/pages/interactives/your-take/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/yourtake/additional/path/items 301 to /pages/interactives/your-take/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/yourtake/additional/path/items",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/pages/interactives/your-take/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/news/pr/ 301 to /pr/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/news/pr/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/pr/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/life/index 301 to /life/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/life/index",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/life/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/life/index/ 301 to /life/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/life/index/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/life/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/money/index 301 to /money/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/money/index",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/money/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/money/index/ 301 to /money/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/money/index/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/money/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/news/index 301 to /news/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/news/index",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/news/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/news/index/ 301 to /news/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/news/index/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/news/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/index 301 to /sports/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/index",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/sports/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/index/ 301 to /sports/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/index/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/sports/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/tech/index 301 to /tech/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/tech/index",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/tech/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/tech/index/ 301 to /tech/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/tech/index/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/tech/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/travel/index 301 to /travel/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/travel/index",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/travel/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/travel/index/ 301 to /travel/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/travel/index/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/travel/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/weather/index 301 to /weather/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/weather/index",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/weather/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},

		fastly.Header{
			Request: fastly.Request{
				Description: "/weather/index/ 301 to /weather/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/weather/index/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/weather/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/experience/ski/ 301 to experience.usatoday.com/ski/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/experience/ski/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/ski/", "experience.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/experience/ski/* 301 to experience.usatoday.com/ski/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/experience/ski/additional/path/items",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/ski/", "experience.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/experience/food-and-wine/ 301 to experience.usatoday.com/food-and-wine/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/experience/food-and-wine/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/food-and-wine/", "experience.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/experience/food-and-wine/* 301 to experience.usatoday.com/food-and-wine/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/experience/food-and-wine/additional/path/items",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/food-and-wine/", "experience.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/picture-gallery/experience/weekend/ 301 to experience.usatoday.com/weekend/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/picture-gallery/experience/weekend/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/weekend/", "experience.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/picture-gallery/experience/weekend/* 301 to experience.usatoday.com/weekend/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/picture-gallery/experience/weekend/something",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/weekend/", "experience.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/experience/weekend/ 301 to experience.usatoday.com/weekend/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/experience/weekend/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/weekend/", "experience.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/experience/weekend/* 301 to experience.usatoday.com/weekend/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/experience/weekend/something",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/weekend/", "experience.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/story/experience/weekend/ 301 to experience.usatoday.com/weekend/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/story/experience/weekend/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/weekend/", "experience.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/story/experience/weekend/* 301 to experience.usatoday.com/weekend/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/story/experience/weekend/something",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/weekend/", "experience.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/video/experience/weekend/ 301 to experience.usatoday.com/weekend/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/video/experience/weekend/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/weekend/", "experience.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/video/experience/weekend/* 301 to experience.usatoday.com/weekend/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/video/experience/weekend/something",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/weekend/", "experience.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/experience/cruise/ 301 to experience.usatoday.com/cruise/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/experience/cruise/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/cruise/", "experience.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/experience/cruise/* 301 to experience.usatoday.com/cruise/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/experience/cruise/something",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/cruise/", "experience.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/picture-gallery/experience/cruise/ 301 to experience.usatoday.com/cruise/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/picture-gallery/experience/cruise/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/cruise/", "experience.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/picture-gallery/experience/cruise/* 301 to experience.usatoday.com/cruise/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/picture-gallery/experience/cruise/something",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/cruise/", "experience.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/experience/destinations/ 301 to experience.usatoday.com/destinations/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/experience/destinations/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/destinations/", "experience.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/experience/destinations/* 301 to experience.usatoday.com/destinations/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/experience/destinations/something",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/destinations/", "experience.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/story/experience/destinations/ 301 to experience.usatoday.com/destinations/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/story/experience/destinations/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/destinations/", "experience.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/story/experience/destinations/* 301 to experience.usatoday.com/destinations/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/story/experience/destinations/something",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/destinations/", "experience.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/experience/america/ 301 to experience.usatoday.com/america/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/experience/america/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/america/", "experience.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/experience/america/* 301 to experience.usatoday.com/america/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/experience/america/something",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/america/", "experience.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/picture-gallery/experience/america/ 301 to experience.usatoday.com/america/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/picture-gallery/experience/america/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/america/", "experience.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/picture-gallery/experience/america/* 301 to experience.usatoday.com/america/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/picture-gallery/experience/america/something",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/america/", "experience.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/story/experience/america/ 301 to experience.usatoday.com/america/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/story/experience/america/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/america/", "experience.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/story/experience/america/* 301 to experience.usatoday.com/america/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/story/experience/america/something",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/america/", "experience.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/video/experience/america/ 301 to experience.usatoday.com/america/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/video/experience/america/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/america/", "experience.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/video/experience/america/* 301 to experience.usatoday.com/america/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/video/experience/america/something",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/america/", "experience.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/experience/south/ 301 to experience.usatoday.com/america/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/experience/south/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/america/", "experience.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/experience/south/* 301 to experience.usatoday.com/america/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/experience/south/something",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/america/", "experience.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/picture-gallery/experience/south/ 301 to experience.usatoday.com/america/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/picture-gallery/experience/south/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/america/", "experience.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/picture-gallery/experience/south/* 301 to experience.usatoday.com/america/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/picture-gallery/experience/south/something",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/america/", "experience.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/videos/experience/south/ 301 to experience.usatoday.com/america/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/videos/experience/south/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/america/", "experience.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/videos/experience/south/* 301 to experience.usatoday.com/america/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/videos/experience/south/something",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/america/", "experience.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/experience/las-vegas/ 301 to experience.usatoday.com/las-vegas/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/experience/las-vegas/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/las-vegas/", "experience.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/experience/las-vegas/* 301 to experience.usatoday.com/las-vegas/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/experience/las-vegas/something",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/las-vegas/", "experience.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/picture-gallery/experience/las-vegas/ 301 to experience.usatoday.com/las-vegas/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/picture-gallery/experience/las-vegas/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/las-vegas/", "experience.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/picture-gallery/experience/las-vegas/* 301 to experience.usatoday.com/las-vegas/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/picture-gallery/experience/las-vegas/something",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/las-vegas/", "experience.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/story/experience/las-vegas/ 301 to experience.usatoday.com/las-vegas/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/story/experience/las-vegas/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/las-vegas/", "experience.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/story/experience/las-vegas/* 301 to experience.usatoday.com/las-vegas/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/story/experience/las-vegas/something",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/las-vegas/", "experience.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/video/experience/las-vegas/ 301 to experience.usatoday.com/las-vegas/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/video/experience/las-vegas/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/las-vegas/", "experience.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/video/experience/las-vegas/* 301 to experience.usatoday.com/destinations/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/video/experience/las-vegas/something",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/las-vegas/", "experience.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/experience/beach/ 301 to experience.usatoday.com/beach/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/experience/beach/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/beach/", "experience.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/experience/beach/* 301 to experience.usatoday.com/beach/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/experience/beach/something",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/beach/", "experience.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/picture-gallery/experience/beach/ 301 to experience.usatoday.com/beach/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/picture-gallery/experience/beach/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/beach/", "experience.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/picture-gallery/experience/beach/* 301 to experience.usatoday.com/beach/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/picture-gallery/experience/beach/something",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/beach/", "experience.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/experience/caribbean/ 301 to experience.usatoday.com/caribbean/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/experience/caribbean/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/caribbean/", "experience.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/experience/caribbean/* 301 to experience.usatoday.com/caribbean/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/experience/caribbean/something",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/caribbean/", "experience.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/picture-gallery/experience/caribbean/ 301 to experience.usatoday.com/caribbean/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/picture-gallery/experience/caribbean/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/caribbean/", "experience.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/picture-gallery/experience/caribbean/* 301 to experience.usatoday.com/caribbean/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/picture-gallery/experience/caribbean/something",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/caribbean/", "experience.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/story/experience/caribbean/ 301 to experience.usatoday.com/caribbean/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/story/experience/caribbean/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/caribbean/", "experience.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/story/experience/caribbean/* 301 to experience.usatoday.com/caribbean/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/story/experience/caribbean/something",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/caribbean/", "experience.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/videos/experience/caribbean/ 301 to experience.usatoday.com/caribbean/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/videos/experience/caribbean/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/caribbean/", "experience.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/videos/experience/caribbean/* 301 to experience.usatoday.com/caribbean/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/videos/experience/caribbean/something",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/caribbean/", "experience.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/mediakit 301 to static.usatoday.com/en/home/<USER>",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/mediakit",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/en/home/", "static.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/mediakit/ 301 to static.usatoday.com/en/home/<USER>",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/mediakit/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/en/home/", "static.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/mediakit/advertising.html 301 to static.usatoday.com/en/advertising/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/mediakit/advertising.html",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/en/advertising/", "static.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/mediakit/editorial.html 301 to static.usatoday.com/en/editorial/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/mediakit/editorial.html",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/en/editorial/", "static.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/mediakit/audience.html 301 to static.usatoday.com/en/audience/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/mediakit/audience.html",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/en/audience/", "static.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/about/ 301 to marketing.usatoday.com/about with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/about/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/about", "marketing.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/annoldenburg 301 to /staff/640/ann-oldenburg/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/annoldenburg",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/staff/640/ann-oldenburg/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/alangomez 301 to /staff/615/alan-gomez/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/alangomez/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/staff/615/alan-gomez/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/mattkrantz 301 to /staff/1035/matt-krantz/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/mattkrantz",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/staff/1035/matt-krantz/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/remrieder 301 to /staff/4055/rem-rieder/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/remrieder",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/staff/4055/rem-rieder/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/jarrettbell 301 to /staff/867/jarrett-bell/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/jarrettbell",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/staff/867/jarrett-bell/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/bobnightengale 301 to /staff/674/bob-nightengale/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/bobnightengale",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/staff/674/bob-nightengale/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/rodneybrooks 301 to /staff/1329/rodney-brooks/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/rodneybrooks",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/staff/1329/rodney-brooks/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/edbaig 301 to /staff/789/edward-c-baig/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/edbaig",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/staff/789/edward-c-baig/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/gregorykorte 301 to /staff/2055/gregory-korte/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/gregorykorte",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/staff/2055/gregory-korte/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sochi 301 to /sports/olympics/2014/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sochi",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/sports/olympics/2014/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/olympics/2014/sports/CE/ 301 to /sports/olympics/2014/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/olympics/2014/sports/CE/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/sports/olympics/2014/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/ncaab/bracket/2013/ 301 to /ncaa/bracket-hub/mens-basketball/2014 with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/ncaab/bracket/2013/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/ncaa/bracket-hub/mens-basketball/2014", "sports.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/ncaab/bracket/2014/ 301 to /ncaa/bracket-hub/mens-basketball/2014 with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/ncaab/bracket/2014/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/ncaa/bracket-hub/mens-basketball/2014", "sports.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/fantasy/baseball/statistics/ 301 to /sports/mlb/statistics/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/fantasy/baseball/statistics/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/sports/mlb/statistics/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/fantasy/football/statistics/ 301 to /sports/nfl/statistics/ with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/fantasy/football/statistics/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/sports/nfl/statistics/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/college/schools/finances/ 301 to sports.usatoday.com/ncaa/finances with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/college/schools/finances/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/ncaa/finances", "sports.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/ncaab/bracket/2015/ 301 to sports.usatoday.com/ncaa/bracket-hub with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/ncaab/bracket/2015/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/ncaa/bracket-hub", "sports.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/ncaaw/polls/ 301 to www.usatoday.com/sports/ncaaw/polls/coaches-poll/",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/ncaaw/polls/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/sports/ncaaw/polls/coaches-poll/", "www.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/ncaab/polls/ 301 to www.usatoday.com/sports/ncaab/polls/coaches-poll/",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/ncaab/polls/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/sports/ncaab/polls/coaches-poll/", "www.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/college/ncaaf/polls/ to www.usatoday.com/sports/ncaaf/polls/coaches-poll/",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/college/ncaaf/polls/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/sports/ncaaf/polls/coaches-poll/", "www.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "www.usatoday.com/sports/ncaaf/polls/amway-coaches-poll/ to www.usatoday.com/sports/ncaaf/polls/coaches-poll/",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/college/ncaaf/polls/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/sports/ncaaf/polls/coaches-poll/", "www.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/college/salaries/ncaaf/coach/ 301 to sports.usatoday.com/ncaa/salaries with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/college/salaries/ncaaf/coach/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/ncaa/salaries", "sports.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/college/salaries/ 301 to sports.usatoday.com/ncaa/salaries with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/college/salaries/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/ncaa/salaries", "sports.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/college/salaries/ncaaf/assistant/ 301 to sports.usatoday.com/ncaa/salaries/football/assistant",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/college/salaries/ncaaf/assistant/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/ncaa/salaries/football/assistant", "sports.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/college/salaries/ncaab/coach/ 301 to sports.usatoday.com/ncaa/salaries/mens-basketball/coach",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/college/salaries/ncaab/coach/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/ncaa/salaries/mens-basketball/coach", "sports.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/college/salaries/all/director/ 301 to sports.usatoday.com/ncaa/salaries/all/director",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/college/salaries/all/director/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/ncaa/salaries/all/director", "sports.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/mlb/event/2019/595899/preview/ 301 to sportsdata /baseball/mlb/summary/595899",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/mlb/event/2019/595899/preview/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/baseball/mlb/summary/595899", "sportsdata.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/nfl/event/2019/76299/recap/ 301 to sportsdata /football/nfl/summary/76299 ",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/nfl/event/2019/76299/recap/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/football/nfl/summary/76299", "sportsdata.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/ncaab/event/2018/994824/recap/ 301 to sportsdata /basketball/ncaab/summary/994824",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/ncaab/event/2018/994824/recap/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/basketball/ncaab/summary/994824", "sportsdata.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/ncaaf/event/2019/76995/boxscore/ 301 to sportsdata /football/ncaaf/summary/76995",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/ncaaf/event/2019/76995/boxscore/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/football/ncaaf/summary/76995", "sportsdata.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/wncaab/event/2018/994824/recap/ 301 to sportsdata /basketball/wncaab/summary/994824",
				Scheme:      "https://",
				Host:        "www.usatodaysportsplus.com",
				Path:        "/sports/wncaab/event/2018/994824/recap/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/basketball/wncaab/summary/994824", "sportsdata.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/nba/event/2019/1006688/preview/ 301 to sportsdata /basketball/nba/summary/1006688",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/nba/event/2019/1006688/preview/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/basketball/nba/summary/1006688", "sportsdata.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/nhl/event/2019/126475/boxscore/ 301 to sportsdata /hockey/nhl/summary/126475",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/nhl/event/2019/126475/boxscore/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/hockey/nhl/summary/126475", "sportsdata.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/nhl/event/2019/126475/matchup/ 301 to sportsdata /hockey/nhl/summary/126475",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/nhl/event/2019/126475/matchup/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/hockey/nhl/summary/126475", "sportsdata.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/ncaaf/navy/ 301 to sportsdata /football/ncaaf/team/66",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/ncaaf/navy/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/football/ncaaf/team/66", "sportsdata.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/ncaaf/navy/roster/ 301 to sportsdata /football/ncaaf/teamroster/66",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/ncaaf/navy/roster/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/football/ncaaf/teamroster/66", "sportsdata.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/nba/76ers/statistics/ 301 to sportsdata /basketball/nba/teamstatistics/404083",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/nba/76ers/statistics/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/basketball/nba/teamstatistics/404083", "sportsdata.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/nba/76ers/ to sportsdata /basketball/nba/team/404083",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/nba/76ers/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"basketball/nba/team/404083", "sportsdata.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/mlb/blue-jays/statistics/ 301 to sportsdata /baseball/mlb/teamstatistics/2984",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/mlb/blue-jays/statistics/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/baseball/mlb/teamstatistics/2984", "sportsdata.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/mlb/blue-jays/ to sportsdata /baseball/mlb/team/2984",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/mlb/blue-jays/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/baseball/mlb/team/2984", "sportsdata.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/nfl/seahawks/ to sportsdata /football/nfl/team/19",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/nfl/seahawks/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/football/nfl/team/19", "sportsdata.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/nfl/seahawks/roster/ 301 to sportsdata /football/nfl/teamroster/19",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/nfl/seahawks/roster/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/football/nfl/teamroster/19", "sportsdata.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/ncaab/central-florida/ 301 to sportsdata /basketball/ncaab/team/2339",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/ncaab/central-florida/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/basketball/ncaab/team/2339", "sportsdata.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/ncaab/central-florida/statistics/ to sportsdata /basketball/ncaab/teamstatistics/2339",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/ncaab/central-florida/statistics/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/basketball/ncaab/teamstatistics/2339", "sportsdata.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/nhl/red-wings/ 301 to sportsdata /hockey/nhl/team/2",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/nhl/red-wings/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/hockey/nhl/team/2", "sportsdata.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/nhl/red-wings/roster/ to sportsdata /hockey/nhl/teamroster/2",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/nhl/red-wings/roster/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/hockey/nhl/teamroster/2", "sportsdata.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/nhl/scores/ to sportsdata /hockey/nhl/scores",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/nhl/scores/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/hockey/nhl/scores", "sportsdata.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/nfl/transactions/ to sportsdata /football/nfl/transactions",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/nfl/transactions/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/football/nfl/transactions", "sportsdata.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/ncaaf/scores/ to sportsdata /football/ncaaf/scores",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/ncaaf/scores/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/football/ncaaf/scores", "sportsdata.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/ncaab/scores/ to sportsdata /basketball/ncaab/scores",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/ncaab/scores/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/basketball/ncaab/scores", "sportsdata.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/ncaab/schedule/ to sportsdata /basketball/ncaab/schedule",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/ncaab/schedule/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/basketball/ncaab/schedule", "sportsdata.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/golf/schedule/ to sportsdata /golf/pga/schedule",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/golf/schedule/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/golf/pga/schedule", "sportsdata.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/soccer/mls/schedule/ to sportsdata /soccer/mls/scores",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/soccer/mls/schedule/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/soccer/mls/scores", "sportsdata.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/soccer/mls/teams/ to sportsdata /soccer/mls/teams",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/soccer/mls/teams/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/soccer/mls/teams", "sportsdata.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/soccer/wc/schedule/ to sportsdata /soccer/wc/scores",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/soccer/wc/schedule/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/soccer/wc/scores", "sportsdata.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/soccer/wc/teams/ to sportsdata /soccer/wc/teams",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/soccer/wc/teams/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/soccer/wc/teams", "sportsdata.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/soccer/wwc/schedule/ to sportsdata /soccer/wwc/scores",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/soccer/wwc/schedule/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/soccer/wwc/scores", "sportsdata.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/soccer/wwc/teams/ to sportsdata /soccer/wwc/teams",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/soccer/wwc/teams/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/soccer/wwc/teams", "sportsdata.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/soccer/epl/schedule/ to sportsdata /soccer/epl/scores",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/soccer/epl/schedule/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/soccer/epl/scores", "sportsdata.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/soccer/epl/teams/ to sportsdata /soccer/epl/teams",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/soccer/epl/teams/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/soccer/epl/teams", "sportsdata.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/repurposing/samsung/* 301 to www.gannett-cdn.com/test/1x1.png with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/repurposing/samsung/something",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/test/1x1.png", "www.gannett-cdn.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/repurposing/samsung/ 301 to www.gannett-cdn.com/test/1x1.png with same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/repurposing/samsung/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/test/1x1.png", "www.gannett-cdn.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/interactive-graphics 301 to /interactive-graphics/ with same path and same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/interactive-graphics",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/interactive-graphics/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/vrstories 301 to /vrstories/ with same path and same protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/vrstories",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/vrstories/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/vrstories 301",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/story/opinion/op-ed/robertrobb/2017/11/15/senate-tax-reform-plan-individual-taxes-worse/*********/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/errors/404/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/featured-newsletter/climatematters/ 301",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/featured-newsletter/climatematters/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/featured-newsletter/climatepoint/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "www.usatoday.com story 301 to www.pressconnects.com with same path and HTTP protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/story/opinion/columnists/2017/05/25/rate-wont-matter-if-trump-russia-colluded/342032001/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"http://www.pressconnects.com/story/opinion/columnists/2017/05/25/rate-wont-matter-if-trump-russia-colluded/342032001/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "www.usatoday.com story 301 to www.jacksonsun.com with same path and HTTP protocol",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/story/opinion/columnists/2017/08/18/if-we-erase-our-history-who-we/569324001/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"http://www.jacksonsun.com/story/opinion/columnists/2017/08/18/if-we-erase-our-history-who-we/569324001/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "www.usatoday.com story 301 redirect to nation now url - mike pence",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/story/opinion/columnists/varvel/2017/11/20/varvel-christian-case-mike-pence-rule/879551001/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/story/opinion/nation-now/2017/11/26/after-weinstein-consider-pence-rule-protect-your-heart-and-marriage-gary-varvel-column/895916001/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "www.usatoday.com story 301 redirect to nation now url - football",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/story/opinion/columnists/brian-dickerson/2017/11/23/football-brain-injuries/885743001/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/story/opinion/nation-now/2017/11/26/nfl-brain-damage-crisis-reviving-suffering-spectator-sport-brian-dickerson-column/895800001/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "www.usatoday.com olympics story 301 redirect to new olympics story",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/story/sports/nfl/super/2016/01/27/super-bowls-ranked-best-worst-all-time/79333008/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/story/sports/nfl/2018/01/24/ranking-ever-super-bowl-game-li-patriots-tom-brady/1061541001/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "www.usatoday.com story 301 to www.pressconnects.com if path is /story/opinion/columnists/2018/02/08/republicans-attacks-fbi/110202076/",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/story/opinion/columnists/2018/02/08/republicans-attacks-fbi/110202076/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"https://www.pressconnects.com/story/opinion/columnists/2018/02/08/republicans-attacks-fbi/110202076/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/321launch 301 to https://www.321launchapp.com/",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/321launch",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"https://www.321launchapp.com/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/P-RP 301 to https://cm.usatoday.com/specialoffer?offer=P-RP",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/P-RP",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"https://cm.usatoday.com/specialoffer?offer=P-RP",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/a-bc 301 to https://cm.usatoday.com/specialoffer?offer=A-BC",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/a-bc",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"https://cm.usatoday.com/specialoffer?offer=A-BC",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/a-bc/ 301 to https://cm.usatoday.com/specialoffer?offer=A-BC",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/a-bc/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"https://cm.usatoday.com/specialoffer?offer=A-BC",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/Y-4 301 to https://cm.usatoday.com/specialoffer?offer=Y-4",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/Y-4",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"https://cm.usatoday.com/specialoffer?offer=Y-4",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/mobile-apps 301 to http://static.usatoday.com/mobile-apps/",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/mobile-apps",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"http://static.usatoday.com/mobile-apps/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/mobile-apps/ 301 to http://static.usatoday.com/mobile-apps/",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/mobile-apps/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"http://static.usatoday.com/mobile-apps/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/storytellersproject/ 301 to http://www.storytellersproject.com",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/storytellersproject/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"http://www.storytellersproject.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/aftermathpod 301 to https://www.cincinnati.com/series/aftermath/",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/aftermathpod",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"https://www.cincinnati.com/series/aftermath/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/aftermathpod/ 301 to https://www.cincinnati.com/series/aftermath/",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/aftermathpod/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"https://www.cincinnati.com/series/aftermath/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/picture-gallery/life/2018/05/17/pictures-from-the-royal-wedding-rehearsal/35018781/ 301",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/picture-gallery/life/2018/05/17/pictures-from-the-royal-wedding-rehearsal/35018781/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/picture-gallery/life/2018/05/17/royal-wedding-scenes-from-the-rehearsal/35018781/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/money/lookup/stocks/XXXX/ 301 to /money",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/money/lookup/stocks/XXXX",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"www.usatoday.com/money/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		// sportspolls.usatoday.com --> www.usatoday.com
		fastly.Header{
			Request: fastly.Request{
				Description: "should 301 redirect for sportspolls.usatoday.com homepage to www.usatoday.com/sports/ncaaf/polls/coaches-poll/",
				Scheme:      "https://",
				Host:        "sportspolls.usatoday.com",
				Path:        "/",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"www.usatoday.com/sports/ncaaf/polls/coaches-poll/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "should 301 redirect for sportspolls.usatoday.com/ncaa/football/polls/coaches-poll to www.usatoday.com/sports/ncaaf/polls/coaches-poll/",
				Scheme:      "https://",
				Host:        "sportspolls.usatoday.com",
				Path:        "/ncaa/football/polls/coaches-poll",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"www.usatoday.com/sports/ncaaf/polls/coaches-poll/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "should 301 redirect for sportspolls.usatoday.com/ncaa/football/polls/cfp-poll/ to www.usatoday.com/sports/ncaaf/polls/college-football-playoff-rankings/",
				Scheme:      "https://",
				Host:        "sportspolls.usatoday.com",
				Path:        "/ncaa/football/polls/cfp-poll/",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"www.usatoday.com/sports/ncaaf/polls/college-football-playoff-rankings/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "should 301 redirect for sportspolls.usatoday.com/ncaa/football/polls/ap-poll/ to www.usatoday.com/sports/ncaaf/polls/ap-poll/",
				Scheme:      "https://",
				Host:        "sportspolls.usatoday.com",
				Path:        "/ncaa/football/polls/ap-poll/",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"www.usatoday.com/sports/ncaaf/polls/ap-poll/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "should 301 redirect for sportspolls.usatoday.com/ncaa/basketball-men/polls/coaches-poll/ to www.usatoday.com/sports/ncaab/polls/coaches-poll/",
				Scheme:      "https://",
				Host:        "sportspolls.usatoday.com",
				Path:        "/ncaa/basketball-men/polls/coaches-poll/",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"www.usatoday.com/sports/ncaab/polls/coaches-poll/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "should 301 redirect for sportspolls.usatoday.com/ncaa/basketball-men/polls/ap-poll/ to www.usatoday.com/sports/ncaab/polls/ap-poll/",
				Scheme:      "https://",
				Host:        "sportspolls.usatoday.com",
				Path:        "/ncaa/basketball-men/polls/ap-poll/",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"www.usatoday.com/sports/ncaab/polls/ap-poll/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "should 301 redirect for sportspolls.usatoday.com/ncaa/basketball-women/polls/coaches-poll/ to www.usatoday.com/sports/ncaaw/polls/coaches-poll/",
				Scheme:      "https://",
				Host:        "sportspolls.usatoday.com",
				Path:        "/ncaa/basketball-women/polls/coaches-poll/",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"www.usatoday.com/sports/ncaaw/polls/coaches-poll/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "should 301 redirect for sportspolls.usatoday.com/ncaa/basketball-women/polls/ap-poll/ to www.usatoday.com/sports/ncaaw/polls/ap-poll/",
				Scheme:      "https://",
				Host:        "sportspolls.usatoday.com",
				Path:        "/ncaa/basketball-women/polls/ap-poll/",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"www.usatoday.com/sports/ncaaw/polls/ap-poll/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "should 301 redirect for sportspolls.usatoday.com/ncaa/baseball/polls/coaches-poll/ to www.usatoday.com/sports/ncaa-baseball/polls/coaches-poll/",
				Scheme:      "https://",
				Host:        "sportspolls.usatoday.com",
				Path:        "/ncaa/baseball/polls/coaches-poll/",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"www.usatoday.com/sports/ncaa-baseball/polls/coaches-poll/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "should 301 redirect for sportspolls.usatoday.com article pages to www.usatoday.com/errors/404/",
				Scheme:      "https://",
				Host:        "sportspolls.usatoday.com",
				Path:        "/2019/06/05/bowling-green-qb-jarret-doege-transfers-to-west-virginia/",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/errors/404/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		/*fastly.Content{
			Request: fastly.Request{
				Description: "sportspolls.usatoday.com/robots.txt shouldn't 404",
				Scheme:      "https://",
				Host:        "sportspolls.usatoday.com",
				Path:        "/robots.txt",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Contains: "User-agent",
			Match:    true,
		},*/
		fastly.Header{
			Request: fastly.Request{
				Description: "should 301 redirect /media/cinematic/video/{id}/{name}/ to /videos/0/0/0/0/0/{id}",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/media/cinematic/video/4073462002/wegmans-support-helps-project-clear-county-waiver-approval/",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"www.usatoday.com/videos/0/0/0/0/0/4073462002/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "should 301 redirect /media/cinematic/gallery/{id}/{name}/ to /picture-gallery/0/0/0/0/0/{id}",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/media/cinematic/gallery/4049301002/eagles-vs-cowboys-photos-from-the-big-game/",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"www.usatoday.com/picture-gallery/0/0/0/0/0/4049301002/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "should 301 redirect /videos/0/0/0/{name}/{id}/ to /videos/0/0/0/0/0/{id}",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/videos/0/0/0/wegmans-support-helps-project-clear-county-waiver-approval/4073462002/",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"www.usatoday.com/videos/0/0/0/0/0/4073462002/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "should 301 redirect /picture-gallery/0/0/0/{name}/{id}/ to /picture-gallery/0/0/0/0/0/{id}",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/picture-gallery/0/0/0/eagles-vs-cowboys-photos-from-the-big-game/4049301002/",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"www.usatoday.com/picture-gallery/0/0/0/0/0/4049301002/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/wnba/teams to sportsdirectinc basketball/wnba-teams",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/wnba/teams/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/data/wnba/teams/teams", "usatoday.sportsdirectinc.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/wnba/injuries to sportsdirectinc basketball/wnba-injuries",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/wnba/injuries/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/data/wnba/injury/injuries", "usatoday.sportsdirectinc.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/nascar/results to sportsdirectinc motorsports/nascar/",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/nascar/results",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/data/nascar/this_weeks_race", "usatoday.sportsdirectinc.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/tennis/schedule to sportsdirectinc /tennis/",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/tennis/schedule",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/data/atp/schedule/schedule", "usatoday.sportsdirectinc.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/motor-sports/schedule/ to sportsdirectinc /motorsports/motorsports",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/motor-sports/schedule/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/motorsports/motorsports", "usatoday.sportsdirectinc.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/injuries/all/ to sportsdata /injuries",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/nfl/bills/injuries/all/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/football/nfl/injuries", "sportsdata.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/transactions/ to sportsdata /transactions",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/nhl/sharks/transactions/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/hockey/nhl/transactions", "sportsdata.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "test for /path case sensitivity",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/country-FInancial",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/story/sponsor-story/country-financial/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "should 301 redirect 2018 election to 2020 page",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/data/elections/2018/results/senate/",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"www.usatoday.com/elections/results/2020-11-03/senate/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "should 301 redirect 2018 election to 2020 page",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/data/elections/2018/results/house/?itm_source=oembed&itm_medium=news&itm_campaign=electionsresults",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"www.usatoday.com/elections/results/2020-11-03/us-house/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "redirect the hyphenated variations for these election results pages to underscore",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/elections/results/race/2020-11-03-ballot-initiative-WA-49434/",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/elections/results/race/2020-11-03-ballot_initiative-WA-49434/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "redirect the hyphenated variations for these election results pages to underscore",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/elections/results/race/2020-11-03-state-house-HI-12127/",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/elections/results/race/2020-11-03-state_house-HI-12127/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		// /weather/ front redirect
		fastly.Header{
			Request: fastly.Request{
				Description: "should 302 redirect to accuweather landing page for /weather/",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/weather/",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: 302,
			Headers: http.Header{
				"Location": []string{
					"www.accuweather.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: " redirect all pages of our broken NFL arrests database to the new experience on Cogent (new ACME tool)",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/nfl/arrests/2019/mia/all/",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"databases.usatoday.com/nfl-arrests/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: " redirect all pages of our broken NFL arrests database to the new experience on Cogent (new ACME tool)",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/mlb/salaries",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"databases.usatoday.com/mlb-salaries/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "redirect subscriber guide for sports+ until its ready",
				Scheme:      "https://",
				Host:        "www.usatodaysportsplus.com",
				Path:        "/subscriberguide/#apps",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"help.usatoday.com/member-benefits",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "redirect newsletters for sports+",
				Scheme:      "https://",
				Host:        "www.usatodaysportsplus.com",
				Path:        "/newsletters/",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"https://profile.usatoday.com/newsletters/manage/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "redirect sports/odds to sportsdata",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/odds",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"https://sportsdata.usatoday.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "redirect sports/nfl/odds to sportsdata",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/nfl/odds",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"https://sportsdata.usatoday.com/football/nfl/odds",
				},
			},
			MatchType: fastly.PartialMatch{},
		}, fastly.Header{
			Request: fastly.Request{
				Description: "redirect sports/nba/odds to sportsdata",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/nba/odds",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"https://sportsdata.usatoday.com/basketball/nba/odds",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "redirect sports/nhl/odds to sportsdata",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/nhl/odds",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"https://sportsdata.usatoday.com/hockey/nhl/odds",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "redirect sports/nhl/odds to sportsdata",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/golf/",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"https://sportsdata.usatoday.com/golf/pga/schedule",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "redirect sports/nhl/odds to sportsdata",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/golf/schedule",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"https://sportsdata.usatoday.com/golf/pga/schedule",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "redirect sports/nhl/odds to sportsdata",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/golf/standings",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"https://sportsdata.usatoday.com/golf/pga/rankings",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "redirect sports/nhl/odds to sportsdata",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/golf/leaderboard",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"https://www.pgatour.com/leaderboard.html?cid=USAT",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "should redirect /jobs/ to /marketplace/jobs/",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/jobs/",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/marketplace/jobs/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "should redirect /jobsearch/ to /marketplace/jobs/",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/jobsearch/",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/marketplace/jobs/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "should redirect /marketplace/jobsearch/ to /marketplace/jobs/",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/marketplace/jobsearch/",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/marketplace/jobs/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "should redirect /videos/embed/2913602001/ to /embed/video/2913602001/",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/videos/embed/2913602001/",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/embed/video/2913602001/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "should redirect /video/embed/2913602001/ to /embed/video/2913602001/",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/video/embed/2913602001/",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/embed/video/2913602001/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		// usatodaysportsplus.com redirects
		// temp homepage redirect to article
		fastly.Header{
			Request: fastly.Request{
				Description: "should redirect usatodaysportsplus.com/ to usatoday.com/story/sports/2022/09/01/usa-today-sports-plus-app-now-available-to-network-subscribers/7941469001/",
				Scheme:      "https://",
				Host:        "www.usatodaysportsplus.com",
				Path:        "/",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: 302,
			Headers: http.Header{
				"Location": []string{
					"usatoday.com/story/sports/2022/09/01/usa-today-sports-plus-app-now-available-to-network-subscribers/7941469001/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "should redirect usatodaysportsplus asset to usatoday",
				Scheme:      "https://",
				Host:        "www.usatodaysportsplus.com",
				Path:        "/story/news/nation/2019/01/29/chickens-lay-eggs-containing-cancer-fighting-treatments-research-says/2701124002/",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"usatoday.com/story/news/nation/2019/01/29/chickens-lay-eggs-containing-cancer-fighting-treatments-research-says/2701124002/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "should redirect usatodaysportsplus.com/search/ to usatoday.com/search/",
				Scheme:      "https://",
				Host:        "www.usatodaysportsplus.com",
				Path:        "/search/",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"usatoday.com/search/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "should redirect usatodaysportsplus.com/errors/404/ to usatoday.com/errors/404/",
				Scheme:      "https://",
				Host:        "www.usatodaysportsplus.com",
				Path:        "/errors/404/",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"usatoday.com/errors/404/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "should redirect usatodaysportsplus.com/tangsvc/td/nba/4/ to usatoday.com/tangsvc/td/nba/4/",
				Scheme:      "https://",
				Host:        "www.usatodaysportsplus.com",
				Path:        "/tangsvc/td/nba/4/",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"usatoday.com/tangsvc/td/nba/4/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "should redirect usatodaysportsplus.com/ads.txt to usatoday.com/ads.txt",
				Scheme:      "https://",
				Host:        "www.usatodaysportsplus.com",
				Path:        "/ads.txt",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"usatoday.com/ads.txt",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
	}

	for _, test := range tests {
		test.Execute(t)
	}
}
