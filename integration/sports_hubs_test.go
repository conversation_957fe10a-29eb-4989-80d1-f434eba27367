package integration

import (
	"net/http"
	"testing"

	fastly "github.com/GannettDigital/go-fastly-tests"
)

func TestSportsHubs(t *testing.T) {
	t.<PERSON>()
	var tests = []fastly.Test{
		fastly.Header{
			Request: fastly.Request{
				Description: "/college-sports gets 200 and goes to the appropriate backend",
				Method:      "GET",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/college-sports/sec/mississippi-state",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
			Headers: http.Header{
				"Gannett-Debug-Path": []string{
					"Sports-Hubs-Service-Chain",
				},
				"Cache-Control": []string{
					"private,no-cache",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/sports/nfl gets 200 and goes to the appropriate backend",
				Method:      "GET",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sports/nfl/nfl-new",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
			Headers: http.Header{
				"Gannett-Debug-Path": []string{
					"Sports-Hubs-Service-Chain",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
	}

	for _, test := range tests {
		test.Execute(t)
	}
}
