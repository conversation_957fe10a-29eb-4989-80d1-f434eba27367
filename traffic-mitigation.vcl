# Table values are based on edge dictionaries in the security-edge-dictionary-ci repo.
sub mitigate_traffic {
  call mitigate_by_ua;
  call mitigate_by_country;
  call mitigate_by_referrer;
  call mitigate_by_client_ip;
  call mitigate_by_continent;
  return;
}

sub mitigate_by_ua {
  if(table.lookup(user_agent_mitigation, req.http.User-Agent)) {
    set req.http.gannett-custom:x-block-request = "true";
  }
  return;
}

sub mitigate_by_country {
  if(table.lookup(country_mitigation, client.geo.country_code)) {
    set req.http.gannett-custom:x-block-request = "true";
  }
  return;
}

sub mitigate_by_referrer {
  # don't re-write original referer request header for table lookup
  declare local var.referer STRING;
  set var.referer = regsub(req.http.Referer, "^https?://?([^:/\s]+).*$", "\1");
  if(table.lookup(referrer_mitigation, var.referer)) {
    set req.http.gannett-custom:x-block-request = "true";
  }
  return;
}

sub mitigate_by_client_ip {
  if(table.lookup(clientip_mitigation, client.ip)) {
    set req.http.gannett-custom:x-block-request = "true";
  }
  return;
}

# Reference Link for continent codes https://docs.fastly.com/vcl/variables/client-geo-continent-code/
sub mitigate_by_continent {
  if(table.lookup(continent_mitigation, client.geo.continent_code)) {
    set req.http.gannett-custom:x-block-request = "true";
  }
  return;
}
