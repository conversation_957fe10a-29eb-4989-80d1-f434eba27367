package integration

import (
	"net/http"
	"testing"

	fastly "github.com/GannettDigital/go-fastly-tests"
)

func TestNewstips(t *testing.T) {
	t.<PERSON>()
	var tests = []fastly.Test{
		fastly.Status{
			Request: fastly.Request{
				Description: "Newstips redirects with out trailing slash",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/newstips",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
		},
	}

	for _, test := range tests {
		test.Execute(t)
	}
}
