package integration

import (
	"net/http"
	"testing"

	fastly "github.com/GannettDigital/go-fastly-tests"
)

func TestBackendOverrides(t *testing.T) {
	t.<PERSON>()
	var tests = []fastly.Test{
		fastly.Status{
			Request: fastly.Request{
				Description: "/vrstories/ serves content",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/vrstories/",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
		},
		fastly.Status{
			Request: fastly.Request{
				Description: "/interactive-graphics/ serves content",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/interactive-graphics/",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
		},
		fastly.Status{
			Request: fastly.Request{
				Description: "/media360/ serves content",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/media360/29906170001/29906170001_5527042617001_5527036162001.mp4",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
		},
		fastly.Status{
			Request: fastly.Request{
				Description: "/amp-stories/ best chocolate serves content",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/amp-stories/best-chocolate-recipes/",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
		},
		fastly.Status{
			Request: fastly.Request{
				Description: "/interactives-content/* serves content",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/interactives-content/money/where-the-jobs-are/index.html",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
		},
		fastly.Status{
			Request: fastly.Request{
				Description: "/usatoday/* serves content",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/usatoday/editorial/wallst.jpg",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
		},
		fastly.Status{
			Request: fastly.Request{
				Description: "/web-sitemap-index.xml serves content",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/web-sitemap-index.xml",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
		},
		fastly.Status{
			Request: fastly.Request{
				Description: "/news-sitemap.xml serves content",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/news-sitemap.xml",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
		},
		fastly.Status{
			Request: fastly.Request{
				Description: "/news_sitemap_home.xml does not serve content and returns a 404",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/news_sitemap_home.xml",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusNotFound,
		},
		fastly.Status{
			Request: fastly.Request{
				Description: "/video-sitemap-index.xml serves content",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/video-sitemap-index.xml",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
		},
		fastly.Status{
			Request: fastly.Request{
				Description: "/border-wall/ serves content",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/border-wall/",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
		},
		fastly.Status{
			Request: fastly.Request{
				Description: "/robots.txt serves content",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/robots.txt",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
		},
		fastly.Status{
			Request: fastly.Request{
				Description: "/ads.txt serves content",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/ads.txt",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
		},
		fastly.Status{
			Request: fastly.Request{
				Description: "/app-ads.txt serves content",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/app-ads.txt",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
		},
		fastly.Status{
			Request: fastly.Request{
				Description: "/news_sitemap_index.xml does not serve content and returns a 410",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/news_sitemap_index.xml",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusGone,
		},
		fastly.Status{
			Request: fastly.Request{
				Description:     "staff/10042282/nate-davis/ returns 200",
				Scheme:          "https://",
				Host:            "www.usatoday.com",
				Path:            "/staff/2646715001/nate-davis/",
				Cookies:         []*http.Cookie{},
				Headers:         http.Header{},
				FollowRedirects: true,
			},
			Status: http.StatusOK,
		},
		fastly.Status{
			Request: fastly.Request{
				Description: "/sitemaps/web-sitemap-2019-01.xml returns 200",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sitemaps/web/web-sitemap-2019-01.xml",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
		},
	}

	for _, test := range tests {
		test.Execute(t)
	}
}
