package integration

import (
	"net/http"
	"testing"

	fastly "github.com/GannettDigital/go-fastly-tests"
)

func TestLottery(t *testing.T) {
	t.<PERSON>()
	var tests = []fastly.Test{
		// Lottery endpoints
		fastly.Header{
			Request: fastly.Request{
				Description: "/lottery gets a 200 status and surrogate",
				Method:      "GET",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/lottery",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
			Headers: http.Header{
				"Gannett-Debug-Path": []string{
					"lottery backend:set",
				},
				"Surrogate-Key": []string{
					"/lottery",
				},
				"Cache-Control": []string{
					"private, no-cache, max-age=0, must-revalidate,no-store",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
	}

	for _, test := range tests {
		test.Execute(t)
	}
}
