package integration

import (
	"net/http"
	"testing"

	fastly "github.com/GannettDigital/go-fastly-tests"
)

func TestWoty(t *testing.T) {
	t.<PERSON>()
	var tests = []fastly.Test{
		// Tailgating endpoints
		fastly.Header{
			Request: fastly.Request{
				Description: "/women-of-the-year-2024 gets a 200 status",
				Method:      "GET",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/women-of-the-year-2024",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
			Headers: http.Header{
				"Gannett-Debug-Path": []string{
					"woty backend:set",
				},
				"Surrogate-Key": []string{
					"/women-of-the-year-2024",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
	}

	for _, test := range tests {
		test.Execute(t)
	}
}
