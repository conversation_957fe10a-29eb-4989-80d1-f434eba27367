package integration

import (
	"net/http"
	"testing"

	fastly "github.com/GannettDigital/go-fastly-tests"
)

func TestTangentHubs(t *testing.T) {
	t.<PERSON>llel()

	var tests = []fastly.Test{
		// sports hub
		fastly.Status{
			Request: fastly.Request{
				Description: "should disable paywall and serve 200 and not be 302 redirected to roadblock for premium article originating from sports hub page referer - when sporthub dict entry has paywall disabled",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/story/sports/preps/kentucky/2025/04/23/kentucky-high-school-baseball-st-xavier-trinity-khsaa-score/83083682007/",
				UA:          UserAgentMacOSChrome,
				Referer:     "https://www.usatoday.com/sports/nfl/",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
		},
		fastly.Status{
			Request: fastly.Request{
				Description: "should serve 302 redirect to paywall roadblock for premium article when Sporthub referer attempted with gnt-fr QSP but no Edge Dict entry was found",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/story/sports/preps/kentucky/2025/04/23/kentucky-high-school-baseball-st-xavier-trinity-khsaa-score/83083682007/?gnt-fr=https%3A%2F%2Fwww.usatoday.com%2Fsports%2Fnfl%2Findianapolis-blahs%2F",
				UA:          UserAgentMacOSChrome,
				Referer:     "https://www.usatoday.com/",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusFound,
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "should set server-timing header with NFL/Indianapolis Colts Sports Hub back button data when referer header is properly set with full path with matching Edge Dict entry",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/story/sports/nfl/colts/2025/07/14/colts-camryn-bynum-free-agent-safety/84298905007/",
				UA:          UserAgentMacOSChrome,
				Referer:     "https://www.usatoday.com/sports/nfl/indianapolis-colts/",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
			Headers: http.Header{
				"Server-Timing": []string{
					"gnt_rd;desc=\"/sports/nfl/indianapolis-colts/~NFL/Indianapolis Colts\"",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "should set server-timing header with NFL/Indianapolis Colts Sports Hub back button data when referer header is set to usatoday.com domain and gnt-fr encoded QSP is set with matching Edge Dict entry",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/story/sports/nfl/colts/2025/07/14/colts-camryn-bynum-free-agent-safety/84298905007/?gnt-fr=https%3A%2F%2Fwww.usatoday.com%2Fsports%2Fnfl%2Findianapolis-colts%2F",
				UA:          UserAgentMacOSChrome,
				Referer:     "https://www.usatoday.com/",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
			Headers: http.Header{
				"Server-Timing": []string{
					"gnt_rd;desc=\"/sports/nfl/indianapolis-colts/~NFL/Indianapolis Colts\"",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "should set server-timing header with NFL/Indianapolis Colts Sports Hub back button data from referer header (if set with relative path) instead of the gnt-fr header",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/story/sports/nfl/colts/2025/07/14/colts-camryn-bynum-free-agent-safety/84298905007/?gnt-fr=https%3A%2F%2Fwww.usatoday.com%2Fsports%2Fnfl%2Fchicago-bears%2F",
				UA:          UserAgentMacOSChrome,
				Referer:     "https://www.usatoday.com/sports/nfl/indianapolis-colts/",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
			Headers: http.Header{
				"Server-Timing": []string{
					"gnt_rd;desc=\"/sports/nfl/indianapolis-colts/~NFL/Indianapolis Colts\"",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "should set server-timing header with NFL/Indianapolis Colts Sports Hub back button data from referer header (if set with relative path) for evergreen urls",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/story/shopping/free-my-lowes-rewards-program-membership/83531915007/",
				UA:          UserAgentMacOSChrome,
				Referer:     "https://www.usatoday.com/sports/nfl/indianapolis-colts/",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
			Headers: http.Header{
				"Server-Timing": []string{
					"gnt_rd;desc=\"/sports/nfl/indianapolis-colts/~NFL/Indianapolis Colts\"",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
	}

	for _, test := range tests {
		test.Execute(t)
	}
}
