package integration

import (
	"net/http"
	"testing"

	fastly "github.com/GannettDigital/go-fastly-tests"
)

func TestUWWhitelistTest(t *testing.T) {
	t.<PERSON>()
	var tests = []fastly.Test{
		fastly.Content{
			Request: fastly.Request{
				Description: "Rebuilding America Mock",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/rebuilding-america/mock/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.<PERSON>ie{},
				Headers:     http.Header{},
			},
			Contains: ContentUWDesktop,
			Match:    true,
		},
		fastly.Content{
			Request: fastly.Request{
				Description: "Elections 2020 whitelist - Single Race Page",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/elections/results/race/2018-08-28-governor-D-FL-10491/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Contains: ContentUWDesktop,
			Match:    true,
		},
		fastly.Content{
			Request: fastly.Request{
				Description: "Elections 2020 whitelist - Date Index Page",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/elections/results/2018-11-06/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Contains: ContentUWDesktop,
			Match:    true,
		},
		fastly.Content{
			Request: fastly.Request{
				Description: "Elections 2020 whitelist - National Office Results Page",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/elections/results/2018-11-06/senate/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Contains: ContentUWDesktop,
			Match:    true,
		},
		fastly.Content{
			Request: fastly.Request{
				Description: "Elections 2020 whitelist - State Page",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/elections/results/2018-11-06/state/arizona/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Contains: ContentUWDesktop,
			Match:    true,
		},
		fastly.Content{
			Request: fastly.Request{
				Description: "video embed page should get UW backend",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/embed/video/110936494/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Contains: ContentUWDesktop,
			Match:    true,
		},
		fastly.Content{
			Request: fastly.Request{
				Description: "/nfl-picks-against-the-spread should get UW backend",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/nfl-picks-against-the-spread/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Contains: ContentUWDesktop,
			Match:    true,
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/50-states/ should 301 to /news",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/50-states/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"www.usatoday.com/news",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Content{
			Request: fastly.Request{
				Description: "/sponsor-story/walmart/ UW backend",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/sponsor-story/walmart/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Contains: ContentUWDesktop,
			Match:    true,
		},
		fastly.Content{
			Request: fastly.Request{
				Description: "How Let Others Know Death - UW backend",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/story/community-hub/funeral-planning/2020/03/24/how-let-others-know-death/4870628002/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Contains: ContentUWDesktop,
			Match:    true,
		},
		fastly.Content{
			Request: fastly.Request{
				Description: "/whatimhearing/ UW Backend",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/whatimhearing/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Contains: ContentUWDesktop,
			Match:    true,
		},
		fastly.Content{
			Request: fastly.Request{
				Description: "/hiddencommonground/ should get hiddencommonground UW backend",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/hiddencommonground/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Contains: ContentUWDesktop,
			Match:    true,
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/hiddencommonground should get hiddencommonground UW backend",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/hiddencommonground",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/hiddencommonground/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		/*
			fastly.Content{
				Request: fastly.Request{
					Description: "/american-south/ should get UW backend on desktop",
					Scheme:      "https://",
					Host:        "www.usatoday.com",
					Path:        "/american-south/",
					UA:          UserAgentWindowsChrome,
					Referer:     "",
					Cookies:     []*http.Cookie{},
					Headers:     http.Header{},
				},
				Contains: ContentUWDesktop,
				Match:    true,
			},
		*/
		fastly.Content{
			Request: fastly.Request{
				Description: "/black-history/ should get black-history UW backend",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/black-history/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Contains: ContentUWDesktop,
			Match:    true,
		},
		fastly.Content{
			Request: fastly.Request{
				Description: "/tracking2020/ should get tracking2020 UW backend",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/tracking2020/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Contains: ContentUWDesktop,
			Match:    true,
		},
		fastly.Content{
			Request: fastly.Request{
				Description: "/at-home/ should get at-home UW backend",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/at-home/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Contains: ContentUWDesktop,
			Match:    true,
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/coronavirus/florida should get UW backend",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/coronavirus/florida",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/coronavirus/florida/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Content{
			Request: fastly.Request{
				Description: "/cobrand/ should get UW backend",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/cobrand/?tag=bestof&title=Best+Of+The+Best&marker=%7BSS-APP%7D",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Contains: ContentUWDesktop,
			Match:    true,
		},
		fastly.Content{
			Request: fastly.Request{
				Description: "/services/cobrand/ should get UW backend",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/services/cobrand/header/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Contains: ContentUWDesktop,
			Match:    true,
		},
		/*
			fastly.Content{
				Request: fastly.Request{
					Description: "/confederate-reckoning/ should get confederate-reckoning UW backend",
					Scheme:      "https://",
					Host:        "www.usatoday.com",
					Path:        "/confederate-reckoning/",
					UA:          "",
					Referer:     "",
					Cookies:     []*http.Cookie{},
					Headers:     http.Header{},
				},
				Contains: ContentUWDesktop,
				Match:    true,
			},
		*/
		fastly.Content{
			Request: fastly.Request{
				Description: "/on-site should get UW backend",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/on-site/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Contains: ContentUWDesktop,
			Match:    true,
		},
		fastly.Content{
			Request: fastly.Request{
				Description: "/lorenzos-locks should get UW backend",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/lorenzos-locks/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Contains: ContentUWDesktop,
			Match:    true,
		},
		fastly.Content{
			Request: fastly.Request{
				Description: "/contests should get contests UW backend",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/contests/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Contains: ContentUWDesktop,
			Match:    true,
		},
		fastly.Content{
			Request: fastly.Request{
				Description: "/nba-on-site should get UW backend",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/nba-on-site/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Contains: ContentUWDesktop,
			Match:    true,
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/errors/404/ should get UW backend",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/errors/404/?uw=true",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: 404,
			Headers: http.Header{
				"Gannett-Debug-Path": []string{
					"uw.usatoday.com backend",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Content{
			Request: fastly.Request{
				Description: "/nba-on-site should get UW backend",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/nba-on-site/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Contains: ContentUWDesktop,
			Match:    true,
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/errors/500/ should get UW backend",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/errors/500/?uw=true",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: 500,
			Headers: http.Header{
				"Gannett-Debug-Path": []string{
					"uw.usatoday.com backend",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
	}

	for _, test := range tests {
		test.Execute(t)
	}
}
