sub select_geo_specific_region {
  if (server.identity ~ "-IAD$") {
    set req.http.gnt-client:geo-region = "east";
    set req.http.Gannett-Debug-Path-Item = "server_identity: " server.identity "IAD block";
    call shared_helpers_general_record_object_path;
  } elsif (server.identity ~ "-SEA$") {
    set req.http.gnt-client:geo-region = "west";
    set req.http.Gannett-Debug-Path-Item = "server_identity: " server.identity "SEA block";
    call shared_helpers_general_record_object_path;
  } else {
    set req.http.gnt-client:geo-region = "east";
    set req.http.Gannett-Debug-Path-Item = "server_identity: " server.identity "east block";
    call shared_helpers_general_record_object_path;
    # regions are not very specific for North America (Canada) so use pops, use server.region for APAC and Asia and catch any new US-West POPs
    if ((server.datacenter ~ "^(DEN|BUR|LAX|LGB|PAO|SJC|SEA|MDW|PWK|YVR)$") || (server.region ~ "^(APAC|Asia|US-West)$")) {
      set req.http.gnt-client:geo-region = "west";
      set req.http.Gannett-Debug-Path-Item = "west block";
      call shared_helpers_general_record_object_path;
    }
  }
}

sub announce_region_selection {
    set req.http.Gannett-Debug-Path-Item = "region: " req.http.gnt-client:geo-region;
    call shared_helpers_general_record_object_path;
}

sub clean_backend_request_headers {
// Dont unset Authorization header for blueprint requests as the backend is Auth protected.
if (req.http.x-origin-expected-host !~ "^usat-(staging|prod).mktplatforms.com") {
  unset bereq.http.Authorization;
  unset bereq.http.cookie;
}
  unset bereq.http.Fastly-Debug;
  unset bereq.http.Gannett-AB;
  unset bereq.http.Gannett-Browser;
  unset bereq.http.Gannett-Debug;
  unset bereq.http.Gannett-Geo-Experience;
  unset bereq.http.gannett-ab;
  unset bereq.http.gannett-debug-path;
  unset bereq.http.gannett-ff;
  unset bereq.http.gannett-ff-hmac;
  unset bereq.http.GUP-Identifiers;
  unset bereq.http.RR;
  unset bereq.http.Gannett-Browser;
  unset bereq.http.x-forwarded-for;
  unset bereq.http.x-forwarded-proto;
  unset bereq.http.x-forwarded-server;
  unset bereq.http.x-gannett-dr-ux-backend;
  unset bereq.http.gannett-custom:x-gannett-protocol;
  unset bereq.http.x-real-ip;
  unset bereq.http.x-ua-vendor;
  unset bereq.http.x-ua-device;
  unset bereq.http.x-varnish;

  unset bereq.http.pragma;
  unset bereq.http.upgrade-insecure-requests;
  unset bereq.http.accept-language;
  unset bereq.http.cache-control;
  unset bereq.http.sec-gpc;
  unset bereq.http.sec-fetch-site;
  unset bereq.http.sec-fetch-mode;
  unset bereq.http.sec-fetch-user;
  unset bereq.http.sec-fetch-dest;
  # TODO should make unsetting of bereq headers more modular and reshareable across origins
  if (req.http.x-origin-expected-host ~ "^tangent") {
    unset bereq.http.fastly-orig-accept-encoding;
    unset bereq.http.fastly-ssl;
    unset bereq.http.gannett-cam-experience-id;
    unset bereq.http.gannett-custom;
    unset bereq.http.gannett-os;
    unset bereq.http.gannett-wally-disable-foulballs;
    unset bereq.http.gannett-wally-version;
    unset bereq.http.stale-on-error;
    unset bereq.http.fastly_info:stale-on-error;
    unset bereq.http.synthetic;
    unset bereq.http.x-append-surrogate;
    unset bereq.http.x-content-access-verification;
    unset bereq.http.x-origin-expected-host;
    unset bereq.http.x-timer;

    unset bereq.http.gannett-usat-request-cam;
    unset bereq.http.Gannett-Custom:tng-supported;
  }
}

# strip incoming headers from request that are intended to only be set internally
sub clean_incoming_request_headers {
  unset req.http.gnt-mobile;
}

sub log_big_query {
    declare local var.debug STRING;
    set var.debug = "req.xid: " req.xid "; req_header_size: " req.header_bytes_read "; req_body_size: " req.body_bytes_read "; resp_header_size: " resp.header_bytes_written;
    log {"syslog "} req.service_id {" bigquery-logs :: "}
      {"{"}
      {""timestamp":""}                 now.sec {"","}
      {""time_elapsed":""}              time.elapsed.usec {"","}
      {""accept_encoding":""}           req.http.Fastly-Orig-Accept-Encoding {"","}
      {""clientIP":""}                  req.http.Fastly-Client-IP {"","}
      {""client_as_number":"}           if(client.as.number!=0, client.as.number, "null") {", "}
      {""client_as_name":""}            if (client.as.name!="", client.as.name, "null") {"", "}
      {""host":""}                      req.http.host {"","}
      {""method":""}                    req.request {"","}
      {""status":""}                    resp.status {"","}
      if (req.http.user-agent,
      {""agent":""}                     cstr_escape(req.http.User-Agent) {"","}, "")
      if (req.http.referer,
      {""referer":""}                   cstr_escape(req.http.Referer) {"","}, "")
      {""x_origin_expected_host":""}    req.http.x-origin-expected-host {"","}
      {""original_url":""}              cstr_escape(req.http.gnt-client:Original-URL) {"","}
      {""url":""}                       cstr_escape(req.url.path) {"","}
      {""req_service_id":""}            req.service_id {"","}
      {""redirect_url":""}              req.http.x-Redir-Url {"","}
      {""resp_xcache":""}               resp.http.X-Cache {"","}
      {""content_type":""}              cstr_escape(resp.http.Content-Type) {"","}
      if (resp.http.set-cookie,
      {""resp_setcookie":"1","}, "")
      {""resp_body_size":""}            resp.body_bytes_written {"","}
      {""fastly_shield":""}             if (req.backend.name ~ "^fastlyshield", "1", "0") {"","}
      {""fastly_response":""}           if (resp.status > 400, resp.response, "") {"","}
      {""fastly_region":""}             server.region {"","}
      {""cache_status":""}              regsub(fastly_info.state, "^(HIT-(SYNTH)|(HITPASS|HIT|MISS|PASS|ERROR|PIPE)).*", "\2\3") {"","}
      {""fastly_restarts":""}           req.restarts {"","}
      {""geo_datacenter":""}            server.datacenter {"","}
      {""geo_continent":""}             client.geo.continent_code {"","}
      {""geo_country_code":""}          client.geo.country_code {"","}
      {""geo_region":""}                client.geo.region{"","}
      {""gcdn":""}                      if (req.http.Gannett-Custom:gcdn, "true", "false") {"","}
      {""qsp":""}                       cstr_escape(req.http.gnt-client:qsp) {"","}
      {""backend":""}                   regsub(req.backend.name, "^.+--", "") {"","}
      if (req.http.gnt-client:rate_limited,
      {""rate_limiting":""}             cstr_escape(req.http.gnt-client:rate_limited) {"","}, "")
      {""client_info": "{ \"client_bot_name\":\""} json.escape(client.bot.name) {"\",\"client_proxy_desc\": \""} json.escape(client.geo.proxy_description) {"\",\"client_proxy_type\": \""} json.escape(client.geo.proxy_type) {"\",\"client_browser_name\": \""} json.escape(client.browser.name) {"\",\"client_browser_version\": \""} json.escape(client.browser.version) {"\",\"client_os_name\": \""} json.escape(client.os.name) {"\",\"client_os_version\": \""} json.escape(client.os.version) {"\",\"ja3\": \""} json.escape(req.http.gnt-client:ja3){"\""} {"}","}
      {""debug": "{ \"req.xid\":\""} json.escape(req.xid) {"\",\"req_header_size\": \""} json.escape(req.header_bytes_read) {"\",\"req_body_size\": \""} json.escape(req.body_bytes_read) {"\",\"resp_header_size\": \""} json.escape(resp.header_bytes_written) {"\",\"accept\": \""} json.escape(req.http.Gannett-Custom:Accept) {"\",\"workspace_bytes_free\": \""} json.escape(workspace.bytes_free){"\""} {"}","}
      {""sigsci": "{ \"x_sigsci_agentresponse\":\""} json.escape(req.http.x-sigsci-agentresponse) {"\",\"x_sigsci_requestid\": \""} json.escape(req.http.x-fastly-ngwaf:requestid) {"\",\"x_sigsci_tags\": \""} json.escape(req.http.x-sigsci-tags){"\""} {"}""}
      {"}"};
}

sub gcs_makestories_auth {
  declare local var.date_stamp STRING;
  declare local var.string_to_sign STRING;
  declare local var.canonical_request STRING;
  declare local var.credential_scope STRING;
  declare local var.canonical_headers STRING;
  declare local var.signed_headers STRING;
  declare local var.signature STRING;

  #setting the bucket access credentials
  declare local var.access_key STRING;
  declare local var.secret_key STRING;
  set var.secret_key="9gSOEtjFP6MTFoWLZnJrBCtWxqb2i2jwYObSsMzd";
  set var.access_key="GOOG1EPV737MN652ALSEZLL27FU7IUUUOOBUELL6L3Z44G2CMRCFWBZGYF4DI";

  set req.http.x-amz-content-sha256 = digest.hash_sha256("");
  set req.http.x-amz-date = strftime({"%Y%m%dT%H%M%SZ"}, now);
  set var.date_stamp = strftime({"%Y%m%d"}, now);

  set var.canonical_headers = "host:" req.http.x-origin-expected-host LF "x-amz-content-sha256:" req.http.x-amz-content-sha256 LF "x-amz-date:" req.http.x-amz-date LF;
  set var.signed_headers = "host;x-amz-content-sha256;x-amz-date";

  set var.canonical_request = "GET" LF req.url.path LF req.url.qs LF var.canonical_headers LF var.signed_headers LF regsub(digest.hash_sha256(""),"^0x", "");

  set var.credential_scope = var.date_stamp "/us-east4/storage/aws4_request";
  set var.string_to_sign = "AWS4-HMAC-SHA256" LF req.http.x-amz-date LF var.credential_scope LF regsub(digest.hash_sha256(var.canonical_request),"^0x", "");
  set var.signature = digest.awsv4_hmac(
      var.secret_key,
      var.date_stamp,
      "us-east4",
      "storage",
      var.string_to_sign);
  set req.http.Authorization = "AWS4-HMAC-SHA256 Credential=" var.access_key "/" var.credential_scope ", SignedHeaders=" var.signed_headers ", Signature=" regsub(var.signature,"^0x", "");

}

sub keep_origin_cache_control {
  set req.http.Gannett-Custom:keep-origin-cache-control = "";
}

# redirect certain URL path patterns deemed invalid
sub redirect_malformed_urls {
    if (!req.http.Gannett-Custom:gcdn) {
      declare local var.path-to-validate STRING;
      set var.path-to-validate = if (req.url.ext == "", req.url.path, req.url.dirname); # to ensure we capture the full path minus the file, as dirname will be truncated if no trailing slash present
      if (
        var.path-to-validate ~ "http(s?)://" ||
        std.strlen(var.path-to-validate) > 1500
      ) {
        set req.http.Gannett-Debug-Path-Item = "404 invalid path";
        call shared_helpers_general_record_object_path;

        set req.url = "/errors/404/";
        call error_page_restart_recv;
      # consolidate sequential slashes e.g. /story//somestory --> /story/somestory
      } elseif (req.url.path ~ "/{2,}") {
        set req.http.x-Redir-Url = "https://" req.http.host regsuball(req.url.path, "/{2,}", "/") if (req.url.qs == "", "", "?" req.url.qs);
        error 701 req.http.x-Redir-Url;
      }
    }
}

sub set_cam_header_to_disabled {
  set req.http.Gannett-USAT-Request-CAM = "Disabled (Never for restricted resource / backend / tangent static path)";
}

sub force_ssl_redirect {
  if (!req.http.Fastly-SSL) {
    set req.http.x-Redir-Url = "https://" req.http.host req.url;
    if (req.http.Gannett-Debug) {
      set req.http.Gannett-Debug-Path = req.http.Gannett-Debug-Path " ; Redirect:http (" req.http.x-Redir-Url ") ;";
    }
    # error 701 "Force SSL";
    error 701 req.http.x-Redir-Url;
  }
}

sub low_env_auth {
  if (( ( req.http.host ~ "^origin-staging" ||
          req.http.host ~ "^staging-" ||
          req.http.host ~ "^ci-" ) &&
          !( client.ip ~ shared_helpers_general_office) &&
          !req.http.Fastly-FF ) || req.request == "FASTLYPURGE" ) {
            if ( table.lookup(access_keys, req.http.Authorization, "NOTFOUND") == "NOTFOUND" &&
                !req.url ~ "^/(index\.php/)?####ADMIN_PATH####/" &&
                !req.url ~ "^/(index\.php/)?(rest|oauth)/" &&
                !req.url ~ "^/pub/static/" &&
                !req.url ~ "^/gcdn/elections/(staging|prod)") {
                error 971;
            }
    }
}

sub redirect_apex_domains_to_www {
  if (req.http.host ~ "^(ci-|staging-|origin-staging-(?:[0-9]-)?|cam-)?(?!w{3})([a-zA-Z0-9-]+)\.([a-z]{3,4})(\.global\.prod\.fastly\.net)?$"){
    set req.http.x-Redir-Url = "https://" if (re.group.1, re.group.1, "") "www." re.group.2 "." re.group.3 req.url.path;
    if (req.http.Gannett-Debug) {
      set req.http.Gannett-Debug-Path-Item = ": Redirect - APEX" " " time.elapsed.msec "ms";
      call shared_helpers_general_record_object_path;
    }
    # error 701 "Moved Permanently";
    error 701 req.http.x-Redir-Url;
  }
}

# set site details supporting both www and non-www subdomains
sub set_site_details {
  /* Sets the following attributes on req.http.Gannett-Custom
    - site-subdomain  e.g. www
    - site-sldomain   e.g. usatoday
    - site-tldomain   e.g. com
    - site-apexdomain e.g. usatoday.com
  */
  call shared_helpers_general_set_site_domain_details_dep;
  if (req.http.Gannett-Custom:site-subdomain == "www") {
    set req.http.Gannett-Custom:site-name = req.http.Gannett-Custom:site-sldomain;
  } else {
    set req.http.Gannett-Custom:site-name = req.http.Gannett-Custom:site-subdomain;
  }
  set req.http.Gannett-Custom:site-code = table.lookup(sitename_to_sitecode, req.http.Gannett-Custom:site-name);
  if (req.http.Gannett-Debug) {
    set req.http.Gannett-Debug-Path-Item = "site-name: "req.http.Gannett-Custom:site-name " site-code: " req.http.Gannett-Custom:site-code " site-apex: " req.http.Gannett-Custom:site-apexdomain;
    call shared_helpers_general_record_object_path;
  }
}

sub uw_set_x_origin_expected_host {
  set req.http.x-origin-expected-host = "uw." req.http.Gannett-Custom:site-apexdomain;
}

sub general_log_bucket {
  declare local var.debug STRING;
  set var.debug = "req.xid: " req.xid "; req_header_size: " req.header_bytes_read "; req_body_size: " req.body_bytes_read "; resp_header_size: " resp.header_bytes_written;
  log {"syslog "} req.service_id {" fastly-log-bucket :: { "}
    {""severity":""}                  if (resp.status > 299,  if (resp.status > 399, if (resp.status > 499, "ERROR", "WARNING"), "NOTICE"), "INFO") {"","}
    {""time":""}                      strftime({"%Y-%m-%dT%H:%M:%S"}, time.start) "." time.start.usec_frac {"Z", "}
    {""timestamp":""}                 now.sec {"","}
    {""time_start":""}                strftime({"%Y-%m-%dT%H:%M:%S"}, time.start) "." time.start.usec_frac {"Z", "}
    {""time_elapsed":""}              time.elapsed.usec {"","}
    {""clientIP":""}                  req.http.Fastly-Client-IP {"","}
    {""http-host":""}                 req.http.host {"","}
    {""response_status":""}           resp.status {"","}
    {""response_reason":""}           if (resp.response, json.escape(resp.response), "null")  {"","}
    {""response_body_size":"}         resp.body_bytes_written {","}
    {""request_agent":""}             json.escape(req.http.User-Agent) {"","}
    {""request_referer":""}           json.escape(req.http.Referer) {"","}
    {""request_method":""}            json.escape(req.method) {"","}
    {""request_protocol":""}          json.escape(req.proto) {"","}
    {""url":""}                       json.escape(req.url) {"","}
    {""httpRequest":"}
      "{ "
        {""requestMethod":""}           json.escape(req.method) {"","}
        {""requestUrl":""}              json.escape(req.url) {"","}
        {""requestSize":""}             req.body_bytes_read {"","}
        {""status":"}                   resp.status {","}
        {""responseSize":""}            resp.body_bytes_written {"","}
        {""userAgent":""}               json.escape(req.http.User-Agent) {"","}
        {""remoteIp":""}                req.http.Fastly-Client-IP {"","}
        {""referer":""}                 json.escape(req.http.Referer) {"","}
        {""protocol":""}                json.escape(req.proto) {"""}
      " },"
    {""req_service_id":""}            req.service_id {"","}
    {""redirect_url":""}              json.escape(req.http.x-Redir-Url) {"","}
    {""resp_xcache":""}               resp.http.X-Cache {"","}
    {""content_type":""}              json.escape(resp.http.Content-Type) {"","}
    {""resp_setcookie":"}             if (resp.http.set-cookie, "true", "false") {","}
    {""fastly_shield":""}             if (req.backend.name ~ "^fastlyshield", "1", "0") {"","}
    {""fastly_response":""}           if (resp.status > 400, resp.response, "") {"","}
    {""fastly_region":""}             server.region {"","}
    {""fastly_server":""}             json.escape(server.identity) {"","}
    {""fastly_is_edge":"}             if(fastly.ff.visits_this_service == 0, "true", "false") {","}
    {""fastly_is_shield":"}           if(req.http.log-origin:shield == server.datacenter, "true", "false") {","}
    {""cache_status":""}              regsub(fastly_info.state, "^(HIT-(SYNTH)|(HITPASS|HIT|MISS|PASS|ERROR|PIPE)).*", "\2\3") {"","}
    {""fastly_restarts":""}           req.restarts {"","}
    {""host":""}                      if (req.http.Fastly-Orig-Host, req.http.Fastly-Orig-Host, req.http.Host) {"","}
    {""geo_country":""}               client.geo.country_name {"","}
    {""geo_city":""}                  client.geo.city {"","}
    {""geo_datacenter":""}            server.datacenter {"","}
    {""geo_continent":""}             client.geo.continent_code {"","}
    {""geo_country_code":""}          client.geo.country_code {"","}
    {""geo_region":""}                client.geo.region{"","}
    {""backend":""}                   regsub(req.backend.name, "^.+--", "") {"","}
    {""client_info":"}
      "{ "
         {""client_bot_name":""}        json.escape(client.bot.name) {"","}
         {""client_proxy_desc":""}      json.escape(client.geo.proxy_description) {"","}
         {""client_proxy_type":""}      json.escape(client.geo.proxy_type) {"","}
         {""client_browser_name":""}    json.escape(client.browser.name) {"","}
         {""client_browser_version":""} json.escape(client.browser.version) {"","}
         {""client_os_name":""}         json.escape(client.os.name) {"","}
         {""client_os_version":""}      json.escape(client.os.version) {"""}
       " },"
    {""sigsci":"}
      "{ "
        {""x_sigsci_agentresponse":""}    json.escape(req.http.x-sigsci-agentresponse) {"","}
        {""x_sigsci_requestid":""}        json.escape(req.http.x-fastly-ngwaf:requestid) {"","}
        {""x_sigsci_tags":""}             json.escape(req.http.x-sigsci-tags) {"""}
      " },"
    {""debug":"}
      "{ "
        {""req.xid":""}                   json.escape(req.xid) {"","}
        {""req_header_size":""}           json.escape(req.header_bytes_read) {"","}
        {""req_body_size":""}             json.escape(req.body_bytes_read) {"","}
        {""resp_header_size":""}          json.escape(resp.header_bytes_written) {"","}
        {""resp_body_size":"}             json.escape(resp.body_bytes_written)
      " }"
    " }";
}