sub process_eu_redirects {
  call shared_eu_set_flag_gdpr;
  if (req.http.Gannett-Custom:X-GDPR-User && client.ip !~ shared_helpers_general_office) {
    if (req.http.host ~ "^(?:ci-|staging-|origin-staging-(?:[0-9]-)?|cam-)?marketing\.") {
      set req.http.hostnametoredirect = "eu-message.usatoday.com";
      set req.http.x-Redir-Url = req.http.gannett-custom:x-gannett-protocol "eu-message.usatoday.com" req.url.path;
      error 702 req.http.x-Redir-Url;
    } else {
      if (!req.http.Gannett-Custom:gcdn) {
        call shared_eu_process_redirect_gdpr;
      }
    }
  }
}
