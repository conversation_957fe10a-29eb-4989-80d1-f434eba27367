include "helpers";
include "redirects";
include "abtest";
include "buildinfo";
include "tables";
include "sports-tables";
include "uw-whitelist";
include "storytelling-agent-paths";
include "eu-experience";
include "traffic-mitigation";
include "tangent";
include "wall-ly";
include "wall-ly_crawler";
include "guplib";
include "cam_disabled";
include "vendor-apps";
include "elections";
include "backend-healthcheck";
include "rate-limit";
include "sports";
include "snippet::shared_critical";
include "snippet::shared_tangent";
include "snippet::shared_helpers_frontend";
include "snippet::shared_prebid";
include "snippet::shared_proxy_vendor";
include "snippet::shared_helpers_general";
include "snippet::shared_proxy_gannett";
include "snippet::shared_eu";
include "snippet::shared_ab_testing";
include "snippet::shared_detection";

sub vcl_recv {

  if (req.http.Fastly-Orig-Accept-Encoding) {
    if (req.http.Fastly-Orig-Accept-Encoding ~ "br") {
      set req.http.Accept-Encoding = "br";
    } elsif (req.http.Fastly-Orig-Accept-Encoding ~ "gzip") {
      set req.http.Accept-Encoding = "gzip";
    } else {
      unset req.http.Accept-Encoding;
    }
  }

  ### Check the status of the backends of this service
  if (req.url == "/usat/backend-status/?gnt-check=1") {
    error 610;
  }

  # Initialization to do only once per request lifetime (the first time through on an edge node before any restarts)
  if (fastly.ff.visits_this_service == 0 && req.restarts == 0) {

    # shared_helpers_frontend - sets client.socket.cwnd size and congestion_algorithm = bbr on the client request
    call shared_helpers_frontend_set_congestion_window;

    # lower environment ci, staging, origin-staging will need authrization header or office network to access
    call low_env_auth;
    # shared_helpers_general_force_ssl_redirect - http - force redirect to https
    call shared_helpers_general_force_ssl_redirect;

    call redirect_apex_domains_to_www;

    set req.http.gnt-client:service-name = "USAT";
    set req.http.gnt-client:Original-URL = req.url.path;
    set req.http.gnt-client:qsp = req.url.qs;
    set req.http.gnt-client:ja3 = tls.client.ja3_md5;
    # Pass service ID and client IP to upstream services
    call shared_helpers_general_set_service_info;

    # rate-limiting
    if (req.url.path !~ "/dcjs/" &&
        req.url.path !~ "/dcc/" &&
        req.url.path !~ "/dc/" &&
        req.url.path !~ "/gciaf/" &&
        req.url.path !~ "^/pbd/" &&
        req.url.path !~ "^/ips/" &&
        req.url.qs !~ "gnt-d-erl" &&
        req.http.Gannett-Debug !~ "1") {
          call gannett_rate_limiting;
        }

    call set_site_details;

    call shared_helpers_frontend_strip_emails_from_qs;

    # proxy to prebid service early
    call shared_prebid_recv;

    # shared_proxy_gannett_vcl_recv_gcias - req.url.path ~ "^/gciaf" - return(pass) - setup backend
    call shared_proxy_gannett_vcl_recv_gcias;

    # shared_proxy_vendor_recv - return(pass) - sets auth and vendor reserve proxy backend for routing conditions defined below
    call shared_proxy_vendor_recv;


    # compute@edge
    if (!req.http.Gannett-FF) {

      # decode url encoded with base64
      if ( req.url.path ~ "^/eUdPVnUv" ) {
        call shared_critical_pre_setup_gnt_compute;

        declare local var.base64_path STRING;
        set var.base64_path = regsub(req.url, "^/eUdPVnUv", "");
        set req.url = "/" digest.base64url_decode(var.base64_path);
      }

      call shared_critical_vcl_recv_rec_ai;
    }

    # Create/save any GUP IDs in req.http.GUP-Identifiers
    call guplib_recv_getset_ids;
  }

 #### wordpress plugin for marketing page ####
if(req.http.host ~ "^(?:ci-|staging-|origin-staging-(?:[0-9]-)?|cam-)?marketing\."){
    ## always cache these images & static assets
    if (req.request == "GET" && req.url.ext ~ "(?i)(css|js|gif|jpg|jpeg|bmp|png|ico|img|tga|webp|wmf)") {
      remove req.http.cookie;
    } else if (req.request == "GET" && req.url.path ~ "(xmlrpc\.php|wlmanifest\.xml)") {
      remove req.http.cookie;
    }

    ### do not cache these files:
    ## never cache the admin pages, or the server-status page
    if (req.request == "GET" && (req.url.path ~ "(wp-admin|bb-admin|server-status)")) {
      set req.http.X-Pass = "1";
    } else if (req.http.X-Requested-With == "XMLHttpRequest" && req.url !~ "recent_reviews") {
    # Do not cache ajax requests except for recent reviews
      set req.http.X-Pass = "1";
    } if (req.url.qs ~ "nocache" ||
        req.url.path ~ "(control\.php|wp-comments-post\.php|wp-login\.php|bb-login\.php|bb-reset-password\.php|register\.php)") {
      set req.http.X-Pass = "1";
    }

    # Remove wordpress_test_cookie except on non-cacheable paths
    if (!req.http.X-Pass && req.http.Cookie:wordpress_test_cookie) {
      remove req.http.Cookie:wordpress_test_cookie;
    }

    if ( req.http.Cookie ) {
      ### do not cache authenticated sessions
      if (req.http.Cookie ~ "(wordpress_|PHPSESSID)") {
        set req.http.X-Pass = "1";
      } else if (!req.http.X-Pass) {
        # Cleans up cookies by removing everything except vendor_region, PHPSESSID and themetype2
        set req.http.Cookie = ";" req.http.Cookie;
        set req.http.Cookie = regsuball(req.http.Cookie, "; +", ";");
        set req.http.Cookie = regsuball(req.http.Cookie, ";(vendor_region|PHPSESSID|themetype2|.*woocommerce.*)=", "; \1=");
        set req.http.Cookie = regsuball(req.http.Cookie, ";[^ ][^;]*", "");
        set req.http.Cookie = regsuball(req.http.Cookie, "^[; ]+|[; ]+$", "");

        if (req.http.Cookie == "") {
          remove req.http.Cookie;
        }
      }
    }
  }


  # determine if request should be a synthetic response
  call shared_helpers_front_end_process_synthetic_rules_recv;

  # This sets a header to determine whether this request pass is subject to CAM restrictions
  call determine_cam_enabled;

  # WALL*LY initial setup and resetting of URL after restarts.
  if (req.http.Gannett-USAT-Request-CAM == "Enabled") {
    call wally_recv_early;
  }

  set req.http.X-Forwarded-Host = req.http.host;

  set req.http.response_info:stale-on-error = "false";
  set req.http.response_info:synthetic = "false";

  # logic to implement our own FF headers
  if (req.http.Gannett-FF-HMAC != digest.hmac_sha256(req.service_id + "HMACgannettHMAC", req.http.Gannett-FF)) {
    unset req.http.Gannett-FF-HMAC;
    unset req.http.Gannett-FF;
  }

  set req.http.response_info:vcl_data = req.vcl;

  # invalidate and reject urls that are no longer used or valid by returning a "410 Gone" status
  if (table.lookup(paths_to_410_usat, req.url.path, "false") == "true" ) {
    error 410 "Gone";
  }

  # invalidate and reject sitemaps that are no longer used or valid by returning a "404 Not Found" status
  if (table.lookup(sitemap_return_404, req.url.path, "false") == "true" ) {
    error 404 "Not found";
  }

  # mraid
  if(req.url.basename == "mraid.js") {
    error 404 "Not found";
  }

  # taxonomy
  if (req.url.path ~ "^/taxonomy/") {
    set req.url = "/errors/404/";
    call error_page_restart_recv;
  }

  #execute these redirects before the Force SSL redirect to minimize 301 hops for SEO purposes
  if(!req.http.Gannett-FF) {
    call process_sportspolls_redirects;
  }

  if (req.http.Fastly-FF) {
    set req.max_stale_while_revalidate = 0s;
  }

  # calls the subroutine for device detection

  if (req.http.Fastly-SSL) {
    set req.http.gannett-custom:x-gannett-protocol = "https://";
  } else {
    set req.http.gannett-custom:x-gannett-protocol = "http://";
  }

  #Enable Image optimizer for the given applications in the conditional logic
  call gnt_vendor_io;

  # protect these rules from getting processed by the shield again
  if(!req.http.Gannett-FF) {
    # for testing weather / gnt_i data - leaving override in vcl_deliver alone
    if (req.http.gannett-geo-ip-override) {
      set client.geo.ip_override = req.http.gannett-geo-ip-override;
    }
    # shared_proxy_gannett_vcl_recv_gcdn - req.url.path ~ "^/gcdn/" - setup x-origin-expect-host
    call shared_proxy_gannett_vcl_recv_gcdn;

    call process_redirects;
    call shared_detection_detect_device;
    call shared_detection_detect_browser;
    call shared_detection_detect_os;

    #Mitigate traffic by any number of methods.
    #refer to traffic-mitigation.vcl for configuring the behavior
    call mitigate_traffic;

    #################### 4xx RULES ################

    call shared_helpers_frontend_status_404_rules;

    call shared_helpers_frontend_status_403_rules;

    # prevents further processing of unsupported requests to Tangent's safeframe domain: www.usatodaynetworkservice.com
    call tangent_restrict_safeframe_domain;

    # correct or redirect to /errors/404/
    call redirect_malformed_urls;

    # election redirects and validate /elections/results/ path
    call process_gnt_elections_redirects;
    if (req.url.path ~ "^/elections/results/") {
      if (req.url.path !~ "^/elections/results/((\d{4}-\d{2}-\d{2})|local|primaries)?(/)?$" &&
        req.url.path !~ "^/elections/results/\d{4}-\d{2}-\d{2}/(presidential|senate|governor|us-house|state)" &&
        req.url.path !~ "^/elections/results/race/(?!(.*2024))(.*)" &&
        req.url.path !~ "^/elections/results/local/(\d{4}-\d{2}-\d{2})" &&
        req.url.path !~ "^/elections/results/2024" &&
        req.url.path !~ "^/elections/results/primaries/((\d{4})|democratic|republican)" &&
        req.url.path !~ "^/elections/results/?$"
      ) {
        set req.http.Gannett-Debug-Path-Item = "elections 404 redir path: " + req.url.path ;
        call shared_helpers_general_record_object_path;
        set req.http.x-Redir-Url = "https://www.usatoday.com/errors/404/";
        error 701 req.http.x-Redir-Url;
      }
    }

    # strip incoming headers from request that are intended to only be set internally
    call clean_incoming_request_headers;

    #### url path --> x-origin-expected-host mappings

    if (req.url.path ~ "^/vrstories/") {
      set req.http.Gannett-Debug-Path-Item = "vrstories";
      call shared_helpers_general_record_object_path;
      set req.http.x-append-surrogate = "vrstories";
      set req.http.x-origin-expected-host = "vrstories-www-gannett-cdn-com.storage.googleapis.com";
      if ( req.url.path == "/vrstories/" ){
        set req.url = regsub(req.url, "^/vrstories/","/index.html");
      } else {
        set req.url = regsub(req.url, "^/vrstories/","/");
      }
      set req.http.Date = now;
      set req.http.Authorization = "AWS GOOGYTGVTYJGDYZCKK46:" digest.hmac_sha1_base64("iGfd4UYsviXG9AnAHMTeXRp/EQXHda8LptJbtEz7", if(req.request == "HEAD", "GET", req.request) LF LF LF req.http.Date LF "/vrstories-www-gannett-cdn-com" req.url.path);
    } elseif (req.url.path ~ "^/7a9b0856ff6e41b4a87512f9e172d375.txt") {
      set req.http.Gannett-Debug-Path-Item = "Bing-IndexNow";
      call shared_helpers_general_record_object_path;
      set req.http.x-append-surrogate = "Bing-IndexNow";
      set req.http.x-origin-expected-host = "content_syndication_bing_production.storage.googleapis.com";
      set req.url = regsub(req.url, "^/","/usatoday.com/");
      set req.http.Date = now;
      set req.http.Authorization = "AWS GOOGYTGVTYJGDYZCKK46:" digest.hmac_sha1_base64("iGfd4UYsviXG9AnAHMTeXRp/EQXHda8LptJbtEz7", if(req.request == "HEAD", "GET", req.request) LF LF LF req.http.Date LF "/content_syndication_bing_production" req.url.path);
    } elseif (req.url.path ~ "^/interactives-content/") {
      set req.http.Gannett-Debug-Path-Item = "interactives-content";
      call shared_helpers_general_record_object_path;
      set req.http.x-append-surrogate = "interactives-content";
      set req.http.x-origin-expected-host = "www.gannett-cdn.com";
    } elseif (req.url.path ~ "^/border-wall") {
      set req.http.Gannett-Debug-Path-Item = "border-wall";
      call shared_helpers_general_record_object_path;
      set req.http.x-append-surrogate = "border-wall";
      set req.http.x-origin-expected-host = "thewall-production.usatoday.com";
    } elseif (req.http.host ~ "sportspolls\.usatoday\.com$" && (req.url.path == "/robots\.txt" || req.url.path ~ "^/.*sitemap\.xml$")) {
      set req.http.x-append-surrogate = "sportspolls_legacy";
      set req.http.x-origin-expected-host = "sportspolls.usatoday.com";
    } elseif (req.url.path ~ "^/media360/") {
      set req.http.Gannett-Debug-Path-Item = "media360 rule";
      call shared_helpers_general_record_object_path;
      set req.http.x-append-surrogate = "media360";
      set req.http.x-origin-expected-host = "media.gannett-cdn.com";
      set req.url = regsub(req.url, "^/media360/","/");
    } elseif (req.url.path ~ "^/sitemaps/") {
      set req.http.Gannett-Debug-Path-Item = "sitemaps -> gannett-cdn";
      call shared_helpers_general_record_object_path;
      set req.http.x-append-surrogate = "sitemaps";
      set req.http.x-origin-expected-host = "www.gannett-cdn.com";
      declare local var.sitemaps_replace_url STRING;
      set var.sitemaps_replace_url = "/sitemaps/" req.http.Gannett-Custom:site-code "/";
      set req.url = regsub(req.url, "^/sitemaps/", var.sitemaps_replace_url);
    } elseif (req.url.path ~ "^/gannett-web/apps/teal/dist/") {
      set req.http.Gannett-Debug-Path-Item = "videoscript -> gannett-cdn";
      call shared_helpers_general_record_object_path;
      set req.http.x-append-surrogate = "videoscript";
      set req.http.x-origin-expected-host = "www.gannett-cdn.com";
      if (req.http.Accept-Encoding ~ "br" && req.url.ext == "js") {
        set req.url = regsub(req.url, "\.min\.js",".min.js.br");
      }
    } elseif (req.url.path ~ "^/(dcjs|dcc)") {
      declare local var.t3-resource STRING;
      declare local var.surrogate-env STRING;
      set var.t3-resource = re.group.1;
      set req.http.Gannett-Debug-Path = req.http.Gannett-Debug-Path " ; "  var.t3-resource " -> gannett-cdn" " " time.elapsed.msec "ms";
      set var.surrogate-env = regsub(req.url, "(/dcjs|/dcc)", "");
      if ( var.surrogate-env ~ "^/(dev|prod|stage)") {
        set req.http.x-append-surrogate = table.lookup(gannett_cdn_proxy,var.t3-resource) "-env-" re.group.1;
      }
      if (req.url.path ~ "^/dcjs/.*q1a2z3.*") {
        set req.http.x-append-surrogate = "data-collection-javascript-static";
      }
      set req.http.x-origin-expected-host = "www.gannett-cdn.com";
      set req.http.Gannett-Custom:disable-vary-cache = "true";
    } elseif (req.url.path ~ "^/amp-stories/") {
      declare local var.amp-story-redirect STRING;
      set var.amp-story-redirect = table.lookup(usat_amp_redirects, req.url.path);
      if (var.amp-story-redirect) {
        set req.http.Gannett-Debug-Path-Item = "amp-stories";
        call shared_helpers_general_record_object_path;
        set req.http.x-append-surrogate = "amp-stories";
        set req.http.x-origin-expected-host = "www.gannett-cdn.com";
        if (req.url.qs) {
          set req.url = var.amp-story-redirect "?" req.url.qs;
        } else {
          set req.url = var.amp-story-redirect;
        }
      }
    } elseif (req.url.path ~ "^/usatoday/" && req.url != "/usatoday/footer.html") {
      set req.http.Gannett-Debug-Path-Item = "usatoday";
      call shared_helpers_general_record_object_path;
      set req.http.x-append-surrogate = "usatoday";
      set req.http.x-origin-expected-host = "www.gannett-cdn.com";
    } elseif (req.url.path ~ "^/(?:web-sitemap-index|news-sitemap|video-sitemap-index)\.xml") {
      set req.http.Gannett-Debug-Path-Item = "sitemaps";
      call shared_helpers_general_record_object_path;
      set req.http.x-append-surrogate = "sitemaps";
      set req.http.x-origin-expected-host = "content_syndication_sitemap_production.storage.googleapis.com";
      declare local var.sitemap_replace_url STRING;
      set var.sitemap_replace_url = "/" req.http.Gannett-Custom:site-code "/";
      set req.url = regsub(req.url, "^/", var.sitemap_replace_url);
      set req.http.Date = now;
      set req.http.Authorization = "AWS GOOGYTGVTYJGDYZCKK46:" digest.hmac_sha1_base64("iGfd4UYsviXG9AnAHMTeXRp/EQXHda8LptJbtEz7", if(req.request == "HEAD", "GET", req.request) LF LF LF req.http.Date LF "/content_syndication_sitemap_production" req.url.path);
      set req.http.Gannett-Debug-Path-Item = req.url " sitemap url";
      call shared_helpers_general_record_object_path;
      set req.backend = F_storage_googleapis_com;
    ####/interactive-graphics/*
    } elseif (req.url.path ~ "^/interactive-graphics/") {
      set req.http.Gannett-Debug-Path-Item = "interactive-graphics";
      call shared_helpers_general_record_object_path;
      set req.http.x-append-surrogate = "interactive-graphics";
      set req.http.x-origin-expected-host = "usat-graphics.storage.googleapis.com";
      if ( req.url.path == "/interactive-graphics/" ){
        set req.url = regsub(req.url, "^/interactive-graphics/","/usat-graphics/index.html");
      } else {
        set req.url = regsub(req.url, "^/interactive-graphics/","/usat-graphics/");
      }
      set req.http.Date = now;
      set req.http.Authorization = "AWS GOOG1EE6ZBBMFACRBPIH2C7OGLF6HMXIL6KWQF42AJJM7RLNPL2KECZ7ASCPY:" digest.hmac_sha1_base64("AFCXxAgjM+nj7ODm+X1+atGVb9/zn+TgueWGJC4p", if(req.request == "HEAD", "GET", req.request) LF LF LF req.http.Date LF "/usat-graphics" req.url.path);
    ####/celebrity-deaths path to use notable deaths backend (celebrity-deaths.usatoday.com)
    } elseif (req.url.path ~ "^/celebrity-deaths") {
      set req.http.x-append-surrogate = "celebrity-deaths";
      set req.http.Gannett-Debug-Path-Item = "celebrity-deaths";
      call shared_helpers_general_record_object_path;
      set req.http.x-origin-expected-host = "celebrity-deaths.usatoday.com";
    #### determine blueprint requests
    } elseif (req.url.path ~ "^/money/blueprint" || req.url.path ~ "^/life/blueprint/l/"){
      call gnt_blueprint_recv;
    ####/most-popular
    } elseif (req.url.path ~ "^/story-of-the-day") {
      set req.http.x-append-surrogate = "most-popular story-of-the-day";
      set req.http.Gannett-Debug-Path-Item = "story-of-the-day(most-popular)";
      call shared_helpers_general_record_object_path;
      set req.http.x-origin-expected-host = "most-popular.usatoday.com";
    ####/public-notices
    } elseif (req.url.path ~ "^/public-notices") {
      set req.http.x-append-surrogate = "public-notices";
      set req.http.Gannett-Debug-Path-Item = "public-notices";
      if (req.url.path == "/public-notices/") {
        # public-notices-web app doesn't support /public-notices/ for home page, must be /
        set req.url = "/" if (req.url.qs == "", "", "?" req.url.qs);
      }
      call shared_helpers_general_record_object_path;
      set req.http.x-origin-expected-host = "public-notices";
    #### betoffer
    } elseif (req.url.path ~ "^/(?:online-betting|bet-offer|betting|betoffer)") {
      call gnt_betting_recv;
    #### Makestories
    } elseif ( req.url.path ~ "^/web-stories/" ) {
    #dont run any trailing slash logic and index.html for assets
      if (req.url.ext !~ "(?i)(vtt|bmp|png|jpeg|svg|webp|gif|mp3|xml|css|js|mp4|jpg)") {
        if (req.url.qs == "") {
          if (req.url.path !~ "/$") {
            set req.http.x-Redir-Url = "https://" req.http.host req.url.path "/";
            error 701 req.http.x-Redir-Url;
          }
          set req.url = regsub(req.url, "/$", "/index.html");
        } else {
          if (req.url.path !~ "/$") {
            set req.http.x-Redir-Url = "https://" req.http.host req.url.path "/?" req.url.qs;
            error 701 req.http.x-Redir-Url;
          }
          set req.url = req.url.path "index.html?" req.url.qs;
        }
      }
      set req.url = regsub(req.url, "^/web-stories","");
      set req.http.Gannett-Debug-Path-Item = "makestories";
      call shared_helpers_general_record_object_path;
      set req.http.x-append-surrogate = "makestories";
      set req.http.x-origin-expected-host = "gannett-makestories.storage.googleapis.com";
      call gcs_makestories_auth;
      ##### cet-front-end-static pages #####
    } elseif (req.url.path ~ "^/pages/interactives/") {
      set req.http.x-append-surrogate = "cet-front-end-static " req.url.path;

      set req.http.Gannett-Debug-Path-Item = "cet-front-end-static";
      call shared_helpers_general_record_object_path;

      set req.http.x-origin-expected-host = "cet-front-end-static.storage.googleapis.com";

      if (req.url.path ~ "^/pages/interactives/assets/") {
        set req.url = std.replace_prefix(req.url.path, "/pages", "");
      } else {
        set req.url = std.replace_prefix(req.url.path, "/pages/interactives/", "/interactives/usatoday/pages/interactives/") "index.html";
      }

      if (req.http.Accept-Encoding ~ "br") {
        set req.url = req.url ".br";
      } elseif (req.http.Accept-Encoding ~ "gzip") {
        set req.url = req.url ".gz";
      }

      set req.http.Date = now;
      set req.http.Authorization = "AWS GOOG1EDVDGLRVCQLRSYLIJGH6AAMXOF7LBLIYN2ENZIRVEYA5BXVZEKKCPIRI:" digest.hmac_sha1_base64("AKBi+cfchYsNTvV48fNFKAxpbDxIPewHH9+0naZF", if(req.request == "HEAD", "GET", req.request) LF LF LF req.http.Date LF "/cet-front-end-static" req.url.path);
    ####/tipico-redirect path to use the redirect service (sportsbet.usatoday.com)
    } elseif (req.url.path ~ "^/tipico-redirect"  && (req.http.host ~ "^(?:ci-|staging-|origin-staging-(?:[0-9]-)?|cam-)?(?:www|usatoday)")) {
      set req.http.x-append-surrogate = "tipico-redirect";
      set req.http.Gannett-Debug-Path-Item = "tipico-redirect";
      call shared_helpers_general_record_object_path;
      // set req.http.x-origin-expected-host = "sportsbet.usatoday.com";
      set req.http.x-Redir-Url = "https://sportsbet.usatoday.com" + req.url ;
      error 701 req.http.x-Redir-Url;
    } elseif ( req.url.path ~ "^/sports/contests" ) {
      call gnt_sportscontests_recv;
    } elseif (req.url.path ~ "^/elections/(results/2024|_next|api|favicon)" || req.url.path ~ "^/elections/results/?$") {
      call gnt_elections_recv;
    } elseif ( req.url.path ~ "^/booklist" ) {
      call gnt_booklist_recv;
    } elseif ( req.url.path ~ "^/comics" ) {
      if (req.http.host != "www.usatoday.com") {
        call gnt_comics_recv;
      }
    } elseif      # TrueTandem Celebrities
    (
      table.lookup(truetandem_celebrity_pages, req.url.path) ||
      req.url.path ~ "^/entertainment/celebrities/_next" ||
      req.url.path ~ "^/entertainment/music/health-check"
    )
    {
      call gnt_celebrityhub_recv;
    } elseif ( req.url.path ~ "^/great-american-tailgate" ) {
      call gnt_tailgating_recv;
    } elseif ( req.url.path ~ "^/great-american-vacation" ) {
      call gnt_vacation_recv;
    } elseif ( req.url.path ~ "^/group-subscriptions-(corporate|education)" ) {
      call gnt_advertise_with_us_recv;
    } elseif ( req.url.path ~ "^/holiday-marketplace" ) {
      call gnt_holiday_marketplace_recv;
    } elseif ( req.url.path ~ "^/home-improvement" ) {
      call gnt_home_improvement_recv;
    } elseif ( req.url.path ~ "^/pets-animals" ) {
      call gnt_petshub_recv;
    } elseif ( req.url.path ~ "^/lottery" ) {
      call gnt_lottery_recv;
    } elseif (            # sports-hubs Logic
      req.url.path ~ "^/sports/college-sports/(big-ten|sec)" ||
      # uncomment the following line for the wider production launch
      # req.url.path ~ "/sports/nfl
      ###### More specific paths so Tangent keeps serving /sports/nfl during Soft Launch. Remove the following THREE Lines for the wider Production launch
      req.url.path ~ "^/sports/nfl/api/query" ||
      req.url.path ~ "^/sports/nfl/_next/" ||
      (req.url.path ~ "^/sports/nfl" && req.url.path ~ "/nfl-new$")
    ){
      call gnt_sports_hubs_recv;
    } elseif ( req.url.path ~ "^/bot-detection" ) {
      call gnt_bot_detection_recv;
    } elseif ( req.url.path ~ "^/money/homefront" ) {
      error 810 "Gone";
    } elseif ( req.url.path ~ "^/real-estate" ) {
      call gnt_realestate_recv;
    } elseif ( req.url.path ~ "^/women-of-the-year-2024" ) {
      call gnt_woty_recv;
    } elseif ( req.url.path ~ "^/women-of-the-year-2025" ) {
      call gnt_woty2025_recv;
    } elseif ( req.url.path ~ "^/womens-sports" ) {
      call gnt_womenssports_recv;
    } elseif ( req.url.path ~ "^/deals" ) {
      call gnt_deals_recv;
    } elseif (req.url.path ~ "^/press-release") {
      call gnt_xpr_media_recv;
    } elseif (req.url.path ~ "^/horoscopes") {
      if (req.url.path !~ "/$" && req.url.path !~ "/static/") {
        set req.http.Gannett-Debug-Path-Item = "trailing slash redirect";
        call shared_helpers_general_record_object_path;
        set req.http.x-Redir-Url = "https://" req.http.host req.url.path "/";
        error 701 req.http.x-Redir-Url;
      }
      declare local var.time TIME;
      declare local var.folder_date STRING;
      # convert to Pacific Standard Time
      set var.time = time.sub(now, 7h);
      set var.folder_date = strftime({"%Y-%m-%d"}, var.time);
      set var.folder_date = "/horoscopes/" + var.folder_date + "/";
      set req.url = regsub (req.url, "/horoscopes/", var.folder_date);
      set req.http.Gannett-Debug-Path = "gannett-horoscope-sanctuary";
      call shared_helpers_general_record_object_path;
      set req.http.x-append-surrogate = "gannett-horoscope-sanctuary";
      set req.http.x-origin-expected-host = "www.gannett-cdn.com";
    } else {
      ##### Tangent / UW #####

      # Don't run Tangent rules on special CAM pages
      if (req.url.dirname != table.lookup(wally_settings, "syntheticDir")) {
        call process_tangent_rules;
      }

      #### UW overrides
      call uw_whitelist;

      # If query param ?uw=true, force Universal Web
      if(req.url ~ "(&|\?)uw=true"){
        set req.http.Gannett-Custom:X-UW-Force = "1";
        set req.http.Gannett-Debug-Path-Item = "uw_param";
        call shared_helpers_general_record_object_path;
        call uw_set_x_origin_expected_host;
      }
    }
  # rules needing to be re-run after a restart and Gannett-FF already present
  } elseif (req.restarts > 0) {
    if (req.url.path ~ "^/errors/") {
      call tangent_error_pages;
      # re-enable clustering after a error page restart
      set req.http.Fastly-Force-Shield = "1";

      set req.http.Gannett-Debug-Path-Item = "FFS enabled on restart: " req.restarts;
      call shared_helpers_general_record_object_path;
    }
  }

  ##### default rule
  if( !req.http.x-origin-expected-host ){
    set req.http.Gannett-Debug-Path-Item = "default rule";
    call shared_helpers_general_record_object_path;
    call uw_set_x_origin_expected_host;
  }

  ##### bare minumum browser version checks for qualified pages
  if (
    req.http.Gannett-Custom:tng-supported != "1" &&
    req.http.x-origin-expected-host !~ "^tangent" &&
    req.url.path != "/services/cobrand/" &&
    req.http.x-origin-expected-host !~ "^gupas" &&
    req.http.x-origin-expected-host !~ "\.mktplatforms\.com" &&
    req.http.x-origin-expected-host !~ "\.usatoday-gdcgroup.com"
  ) {
    call shared_helpers_frontend_supported_browser_check_gannett;
  }

  #################
  ### trivia ###
  #################
  if(req.http.host ~ "^(?:ci-|staging-|origin-staging-(?:[0-9]-)?|cam-)?trivia\."){
      set req.http.x-origin-expected-host = "trivia.usatoday.com";
  }

  if(req.http.host ~ "^(?:ci-|staging-|origin-staging-(?:[0-9]-)?|cam-)?gaming\."){
    set req.http.x-origin-expected-host = "gaming.usatoday.com";
  }

  ##################
  ###  newstips  ###
  ##################

  if (req.http.host ~ "usatoday\.com$" && req.url ~ "^/newstips/") {
    set req.http.x-origin-expected-host = "newstips.usatoday.com";
    set req.url = regsub(req.url, "^/newstips","");
  }

  # Apply actual backend selection
  if(req.http.x-origin-expected-host == "www.gannett-cdn.com"){
      set req.http.Gannett-Debug-Path-Item = "www.gannett-cdn.com backend";
      call shared_helpers_general_record_object_path;
      set req.http.x-append-surrogate = req.http.x-append-surrogate " " "gannett-cdn";
      set req.backend = F_www_gannett_cdn_com;
  } else if (req.http.x-origin-expected-host == "thewall-production.usatoday.com"){
      set req.http.Gannett-Debug-Path-Item = "thewall-production.usatoday.com backend";
      call shared_helpers_general_record_object_path;
      set req.backend = F_thewall_production_usatoday_com;
  } else if (req.http.x-origin-expected-host == "media.gannett-cdn.com"){
      set req.http.Gannett-Debug-Path-Item = "media.gannett-cdn.com backend";
      call shared_helpers_general_record_object_path;
      set req.backend = F_chain_media_gannett_cdn_com;
  } else if(req.http.x-origin-expected-host ~ "storage.googleapis.com"){
      set req.http.Gannett-Debug-Path-Item = "storage.googleapis.com backend";
      call shared_helpers_general_record_object_path;
      set req.backend = F_storage_googleapis_com;
  } else if(req.http.x-origin-expected-host ~ "^uw\."){
    call check_uw_unsupported_paths;
    if (!req.http.Gannett-Custom:X-UW-Unsupported) {
      set req.http.Gannett-Debug-Path-Item = "uw." req.http.Gannett-Custom:site-apexdomain " backend";
      set req.http.x-append-surrogate = "universal_web";
      set req.http.Gannett-Custom:UW = "1";
      call shared_helpers_general_record_object_path;

      # UW doesn't support evergreen asset URLs without date - send to /errors/404/
      call shared_helpers_frontend_uw_evergreen_assets_404;

      # UW doesn't support brotli, fallback to gzip
      call shared_helpers_frontend_brotli_fallback;

      # check/set earlyhints & link preload
      call shared_helpers_frontend_check_early_hints_preload_support;
      call shared_helpers_frontend_usat_uscp_set_earlyhints_gup_user_data;

      # select a region based on the east weight
      if(randombool(std.atoi(table.lookup(weight_shift, "universal-web")), 100)) {
        set req.backend = F_universal_web_east;
        set req.http.Gannett-Debug-Path-Item = "region: uw-east";
        call shared_helpers_general_record_object_path;
      } else {
        set req.backend = F_universal_web_west;
        set req.http.Gannett-Debug-Path-Item = "region: uw-west";
        call shared_helpers_general_record_object_path;
      }
      # check that the backend is healthy
      if(req.backend == F_universal_web_east && !req.backend.healthy) {
        set req.backend = F_universal_web_west;
        set req.http.Gannett-Debug-Path-Item = "uw-east unhealthy";
        call shared_helpers_general_record_object_path;
      } else if(req.backend == F_universal_web_west && !req.backend.healthy) {
        set req.backend = F_universal_web_east;
        set req.http.Gannett-Debug-Path-Item = "uw-west unhealthy";
        call shared_helpers_general_record_object_path;
      }
    }
  } else if(req.http.x-origin-expected-host == "gaming.usatoday.com"){
        set req.http.x-append-surrogate = "gaming-chalkline";
        set req.http.Gannett-Debug-Path-Item = "chalkline backend";
        call shared_helpers_general_record_object_path;
        set req.backend = F_gaming_chalkline;
        return(pass);
  } else if (req.http.x-origin-expected-host == "newstips.usatoday.com"){
      set req.http.Gannett-Debug-Path-Item = "newstips";
      call shared_helpers_general_record_object_path;
      set req.backend = F_newstips_usatoday_com;
      return(pass);
  } else if (req.http.x-origin-expected-host == "sportspolls.usatoday.com"){
      set req.http.Gannett-Debug-Path-Item = "sportspolls";
      call shared_helpers_general_record_object_path;
      set req.backend = F_vip_lb_wordpress_com;
      return(pass);
  } else if (req.http.x-origin-expected-host == "sportsbet.usatoday.com"){
      set req.http.Gannett-Debug-Path-Item = "sportsbet.usatoday.com backend";
      call shared_helpers_general_record_object_path;
      set req.backend = F_chain_sportsbet_usatoday_com;
  ## Set specific backend for public-notices staging or production #
  } else if (req.http.x-origin-expected-host == "public-notices" || req.http.Gannett-Custom:public-notices) {
    if (req.http.Gannett-Debug) {
      set req.http.Gannett-Debug-Path = req.http.Gannett-Debug-Path " ; " "backend: public-notices " time.elapsed.msec "ms";
    }
      call shared_helpers_general_record_object_path;
      set req.backend = F_public_notices;
      set req.http.Gannett-Custom:public-notices = "";
      # must set to desired public-notices service hosts
      if (req.http.host ~ "^origin-staging") {
        set req.http.x-origin-expected-host = "public-notices-web-cdn-preprod.gannettdigital.com";
      } else {
        set req.http.x-origin-expected-host = "public-notices-web-cdn-prod.gannettdigital.com";
      }
      set req.http.Gannett-Custom:skip-set-surrogates = true;
      #disable CAM for request
      call set_cam_header_to_disabled;
      return(pass);
  } else if (req.http.x-origin-expected-host == "celebrity-deaths.usatoday.com"){
      set req.http.Gannett-Debug-Path-Item = "celebrity-deaths.usatoday.com backend";
      call shared_helpers_general_record_object_path;
      set req.backend = F_celebrity_deaths_east_usatoday_com;
        if(req.backend == F_celebrity_deaths_east_usatoday_com && !req.backend.healthy) {
          set req.backend = F_celebrity_deaths_west_usatoday_com;
          set req.http.Gannett-Debug-Path-Item = "celebrity-east unhealthy";
          call shared_helpers_general_record_object_path;
        } else if(req.backend == F_celebrity_deaths_west_usatoday_com && !req.backend.healthy) {
          set req.backend = F_celebrity_deaths_east_usatoday_com;
          set req.http.Gannett-Debug-Path-Item = "celebrity-west unhealthy";
          call shared_helpers_general_record_object_path;
        }
  } else if (req.http.x-origin-expected-host == "most-popular.usatoday.com"){
      set req.http.Gannett-Debug-Path-Item = "most-popular.usatoday.com backend";
      call shared_helpers_general_record_object_path;
      set req.backend = F_most_popular_east_usatoday_com;
        if(req.backend == F_most_popular_east_usatoday_com && !req.backend.healthy) {
          set req.backend = F_most_popular_west_usatoday_com;
          set req.http.Gannett-Debug-Path-Item = "most-popular east unhealthy";
          call shared_helpers_general_record_object_path;
        } else if(req.backend == F_most_popular_west_usatoday_com && !req.backend.healthy) {
          set req.backend = F_most_popular_west_usatoday_com;
          set req.http.Gannett-Debug-Path-Item = "most-popular west unhealthy";
          call shared_helpers_general_record_object_path;
        }
  } else if (req.http.x-origin-expected-host ~ "\.usatoday-gdcgroup.com"){
      call gnt_betting_recv_backend;
  } else if (req.http.x-origin-expected-host ~ "^usat-(staging|prod).mktplatforms.com") {
      call gnt_blueprint_backend;
  } else if (req.http.Gannett-Custom:sportscontests == "1" ){ # /sports/contests backend logic
      call gnt_sportscontests_backend;
  } else if (req.http.Gannett-Custom:elections == "1"){
      call gnt_elections_backend;
  } else if (req.http.Gannett-Custom:booklist == "1" ){ # /booklist backend logic
      call gnt_booklist_backend;
  } else if (req.http.Gannett-Custom:celebrityhub == "1" ){ # /celebrity-hub backend logic #
      call gnt_celebrityhub_backend;
  } else if (req.http.Gannett-Custom:comics == "1" ){ ## /comics backend logic #
      call gnt_comics_backend;
  } else if (req.http.Gannett-Custom:tailgating == "1" ){ # /tailgating backend logic
      call gnt_tailgating_backend;
  } else if (req.http.Gannett-Custom:vacation == "1" ){ # /vacation backend logic
      call gnt_vacation_backend;
  } else if (req.http.Gannett-Custom:advertisewithus == "1" ){ # /vacation backend logic
      call gnt_advertise_with_us_backend;
  } else if (req.http.Gannett-Custom:holiday-marketplace == "1" ){ # /holiday-marketplace backend logic
      call gnt_holiday_marketplace_backend;
  } else if (req.http.Gannett-Custom:home-improvement == "1" ){ # /home-improvement backend logic
      call gnt_home_improvement_backend;
  } else if (req.http.Gannett-Custom:petshub == "1" ){ # /pets-hub
      call gnt_petshub_backend;
  } else if (req.http.Gannett-Custom:sportshubs){ # /Sports-hub
      call gnt_sports_hubs_backend;
  } else if (req.http.Gannett-Custom:bot-detection){ # /bot-detection
      call gnt_bot_detection_backend;
  } else if (req.http.Gannett-Custom:lottery == "1" ){ # /lottery backend logic
      call gnt_lottery_backend;
  } else if (req.http.Gannett-Custom:realestate == "1" ){ # /real_estate backend logic
      call gnt_realestate_backend;
  } else if (req.http.Gannett-Custom:woty == "1" ){ # /woman_of_the_year backend logic
      call gnt_woty_backend;
  } else if (req.http.Gannett-Custom:woty2025 == "1" ){ # /woman_of_the_year_2025 backend logic
      call gnt_woty2025_backend;
  } else if (req.http.Gannett-Custom:womenssports == "1" ){ # /womans-sports backend logic
      call gnt_womenssports_backend;
  } else if (req.http.Gannett-Custom:deals == "1" ){ # /deals backend logic
    call gnt_deals_backend;
  } else if (req.http.Gannett-Custom:xpr_media ){
    call gnt_xpr_media_backend;
  } else if (req.http.x-origin-expected-host ~ "^tangent"){
      # should enable for all backends
      if (req.url.qs ~ "gnt-debug") {
        set req.http.Gannett-Debug = "1";
        set req.http.Fastly-Debug = "1";
      }
      #### tangent fronts 404 response for long section paths
      call shared_tangent_404_long_section_paths;
      # via loadbalancing w/ healthchecks
      call select_tangent_backend;
  }

  # Storytelling Studio routes
  # this handles /in-depth and /storytelling, plus a few others
  call s2_agent_path_select;

  # Set the disable foulball header for Wall*ly for all markets.
  set req.http.Gannett-Wally-Disable-Foulballs = "Yes";

  if (req.http.Gannett-USAT-Request-CAM == "Enabled") {
    # WALL*LY: Figure out who the visiting user is for paywall purposes.
    call wally_recv_late;
  } else {
    # So that the WALL*LY synthetic resources can still be served when disabled.
    call wally_recv_url_routing;
  }

  # This code executes on edge which is always hit as opposed to the shield. Any code that needs to run per user/hit
  # should run on edge instead of shield.
  if (!req.http.Gannett-FF || (req.restarts > 0 && req.http.Gannett-FF == server.identity)) {

    if(req.http.Cookie:gnt_eid){
      set req.http.Gannett-Cam-Experience-Id:existing_gnt_eid = req.http.Cookie:gnt_eid;
    }

    ## Set experience to cam_eid cookie if it exists
    if (req.http.Cookie:cam_eid){
      set req.http.Gannett-Cam-Experience-Id:segment = req.http.Cookie:cam_eid;
    } else {
      ## Default experience is control
      set req.http.Gannett-Cam-Experience-Id:segment = "control";
      if (req.http.Cookie:gup_lng){
        ## Set default experience to subscriber if user is subscribed
        if(req.http.Cookie:gup_lng ~ {"hma%22%3A%20true"}){
          set req.http.Gannett-Cam-Experience-Id:segment = "subscriber";
        }
        ## Set default experience to registrant if user is registered
        elseif(req.http.Cookie:gup_lng ~ {"ret-usr%22%3A%20true"}){
          set req.http.Gannett-Cam-Experience-Id:segment = "registrant";
        }
      }
    }

    if (req.http.Gannett-Cam-Experience-Id:segment ~ "Infinity(Med|High)" && req.http.Gannett-Custom:site-name == "usatodaysportsplus") {
      ## user is authed
      if (req.http.Cookie:gup_lng && req.http.Cookie:gup_lng ~ {"auth%22%3A%20true"}) {
        set req.http.Gannett-Cam-Experience-Id:segment = "Reg" req.http.Gannett-Cam-Experience-Id:segment;
      } else{
        set req.http.Gannett-Cam-Experience-Id:segment = "Anon" req.http.Gannett-Cam-Experience-Id:segment;
      }
    }

    ## Set experience to cam_eid_override if set by cookie or QSP
    if (req.http.Cookie:cam_eid_override) {
      set req.http.Gannett-Cam-Experience-Id:existing_cam_eid_override = req.http.Cookie:cam_eid_override;
      set req.http.Gannett-Cam-Experience-Id:segment = req.http.Gannett-Cam-Experience-Id:existing_cam_eid_override;
    }
    declare local var.cam_eid_override_qsp STRING;
    set var.cam_eid_override_qsp = subfield(req.url.qs, "cam-eid-override", "&");

    if (var.cam_eid_override_qsp != "" && var.cam_eid_override_qsp != req.http.Gannett-Cam-Experience-Id:existing_cam_eid_override) {
      set req.http.Gannett-Cam-Experience-Id:cam_eid_override = var.cam_eid_override_qsp;
      set req.http.Gannett-Cam-Experience-Id:segment = req.http.Gannett-Cam-Experience-Id:cam_eid_override;
    }

    # CMREV-5574 DMA-TPP
    if (req.http.Gannett-Cam-Experience-Id:segment ~ "(Anon|Reg)Infinity(Low|Med|High)" && table.lookup(ttp_dma_codes_usat, std.itoa(client.geo.metro_code)) == "true")
    {
        if (req.http.Gannett-Debug) {
          set req.http.Gannett-Debug-Path = req.http.Gannett-Debug-Path "; DMA match";
        }
        set req.http.Gannett-Cam-Experience-Id:segment = "USAT_TARGET_DMA_TPP";
        set req.http.gnt-dma = "1";
    }
    else {
      if (req.http.Gannett-Debug) {
          set req.http.Gannett-Debug-Path = req.http.Gannett-Debug-Path "; No DMA match";
        }
    }

    call shared_tangent_back_button_banner_hub_recv;

    # set header to be used to prevent cookie logic from running on subresources
    call shared_helpers_tangent_can_run_cookie_logic_recv;

    # Process a/b test bucketing and headers.
    if (
      req.http.host ~ "^(?:ci-|staging-|origin-staging-(?:[0-9]-)?|cam-)?www" &&
      req.http.Gannett-Custom:can_run_cookie_logic
    ) {
      call abtest_recv_headers;
      if (req.http.Gannett-Debug) {
        set req.http.Gannett-Debug-Path-Item = "abtest_recv_headers";
        call shared_helpers_general_record_object_path;
      }
    }

    if (req.http.Gannett-Cam-Experience-Override) {
      set req.http.Gannett-Cam-Experience-Id:segment = req.http.Gannett-Cam-Experience-Override;
    } else {
      set req.http.Gannett-Cam-Experience-Id:segment = req.http.Gannett-Cam-Experience-Id:segment ":" req.http.Gannett-AB:gnt-sb;
    }

    # Only keep around cookies that we know we need.
    call recv_sanitize_cookie_header;
    if (req.http.Gannett-Debug) {
      set req.http.Gannett-Debug-Path-Item = "recv_sanitize_cookie_header";
      call shared_helpers_general_record_object_path;
    }

    # check for entropy qsp override
    call shared_helpers_frontend_update_gnt_i_entropy;

    #############################
    ### tangent - redesigned nav on UW ###
    #############################
    call tangent_nav_on_uw;

  }

  # WALL*LY: Complete paywall processing.
  # If no valid firefly_akamai cookie is found AND origin healthcheck passes, will shortcut to pass stage.
  if (req.backend.healthy) {
    if (req.http.Gannett-USAT-Request-CAM == "Enabled") {
      call wally_recv_complete;
    }
  }

  # Process a/b test backend overrides.
  if (
    req.http.host ~ "^(ci-|staging-|origin-staging-(?:[0-9]-)?|cam-)?www" &&
    req.http.Gannett-Custom:can_run_cookie_logic
  ) {
    call abtest_recv_complete;
    if (req.http.Gannett-Debug) {
      set req.http.Gannett-Debug-Path-Item = "abtest_recv_complete";
      call shared_helpers_general_record_object_path;
    }
  }

#FASTLY recv
  # shared_proxy_gannett_vcl_recv_after_gcdn - req.http.Gannett-Custom:gcdn - unset cookie and some paths return pass
  call shared_proxy_gannett_vcl_recv_after_gcdn ;

  if (req.request != "HEAD" && req.request != "GET" && req.method != "POST" && req.request != "FASTLYPURGE") {
    return(pass);
  }

  if (req.method == "POST" &&
      req.url.path !~ "^/sports/contests/api/data" &&
      req.url.path !~ "^/booklist/api/graphql" &&
      req.url.path !~ "^/elections/api/data") {
    return(pass);
  }

  # logic to implement our own FF header
  if (!req.http.Gannett-FF) {
    set req.http.Gannett-FF = server.identity;
  } elsif (!std.suffixof(req.http.Gannett-FF, server.identity)) {
   set req.http.Gannett-FF = req.http.Gannett-FF + ", " + server.identity;
  }
  # secret key should match the one at the top
  set req.http.Gannett-FF-HMAC = digest.hmac_sha256(req.service_id + "HMACgannettHMAC", req.http.Gannett-FF);

  set req.http.X-Forwarded-Proto = "https";

  if (req.url.path ~ "/(\d{4})/(\d{2})/(\d{2})/") {
    if(!req.http.x-append-surrogate){
      # set full date (2017-01-01) as surrogate key
      set req.http.x-append-surrogate = re.group.1 "-" re.group.2 "-" re.group.3;
      # set year-month as surrogate key
      set req.http.x-append-surrogate = req.http.x-append-surrogate " " re.group.1 "-" re.group.2;
    } else {
      # set full date (2017-01-01) as surrogate key
      set req.http.x-append-surrogate = req.http.x-append-surrogate " " re.group.1 "-" re.group.2 "-" re.group.3;
       # set year-month as surrogate key
      set req.http.x-append-surrogate = req.http.x-append-surrogate " " re.group.1 "-" re.group.2;
    }
  }


  if (req.url.path ~ "^/story/") {
    set req.http.x-append-surrogate = req.http.x-append-surrogate " asset_story asset_story_usatoday";
  }

  if (req.url.path ~ "^/videos/") {
    set req.http.x-append-surrogate = req.http.x-append-surrogate " asset_video asset_video_usatoday";
  }

  if (req.url.path ~ "^/picture-gallery/") {
    set req.http.x-append-surrogate = req.http.x-append-surrogate " asset_gallery asset_gallery_usatoday";
  }

   if (req.url.path ~ "^/media/") {
     set req.http.x-append-surrogate = req.http.x-append-surrogate " media media_usatoday";
   }

   if (req.url.path ~ "^/search/") {
     set req.http.x-append-surrogate = req.http.x-append-surrogate " search search_usatoday";
   }

  return(lookup);
}

sub vcl_fetch {
  # shared_helpers_frontend - set log_timing header
  call shared_helpers_frontend_log_timing_fetch;

  #FASTLY fetch

  #### wordpress plugin for marketing
  # just in case the request snippet for x-pass is not set we pass here
  if ( req.http.x-pass ) {
      return(pass);
  }

  if (!req.backend.is_shield) {
    set beresp.http.backend_ip = beresp.backend.ip;
  }

  # Call the subroutine to set election SKs
  if ( req.http.Gannett-Custom:elections ) {
    call gnt_elections_sk_setting;
  }

  # Call the subroutine to set election SKs
  if ( req.http.Gannett-Custom:sportscontests ) {
    call gnt_sportscontests_sk_setting;
  }

  # prebid - don't override vcl_fetch logic from prebid service
  call shared_prebid_fetch;

  # shared_proxy_vendor_fetch - return(deliver) - early exit from vcl_fetch to avoid override of vcl_fetch logic from reverse-proxy-vcl service
  call shared_proxy_vendor_fetch;

  #rec-ai ttl set
  call shared_critical_vcl_fetch_rec_ai;

  # shared_proxy_gannett_vcl_fetch_gcias - req.http.x-origin-expected-host ~ "gcias-compute.usatoday.com" - return pass - setup ttl and cache-control
  call shared_proxy_gannett_vcl_fetch_gcias;

  ## surrogates
  if (!req.backend.is_shield) {
     if (beresp.http.Surrogate-Key) {
       if (beresp.status != 200) {
          set beresp.http.Surrogate-Key = beresp.http.Surrogate-Key " " req.url.dirname " " beresp.status " " req.http.x-append-surrogate;
       } else {
          set beresp.http.Surrogate-Key = beresp.http.Surrogate-Key " " req.url.dirname " " req.http.x-append-surrogate;
       }
     } else {
        if (beresp.status != 200) {
          set beresp.http.Surrogate-Key = req.url.dirname " " beresp.status " " req.http.x-append-surrogate;
        } else {
          set beresp.http.Surrogate-Key = req.url.dirname " " req.http.x-append-surrogate;
        }
     }
  }

  # PAAS-5291 - if backend gives us a redirect to http, convert it to https before caching
  if (beresp.status == 301 || beresp.status == 302){
    if (beresp.http.Location ~ "^http://"){
      set beresp.http.Location = regsub(beresp.http.Location, "http://", "https://");
    }
  }

  # WALL*LY: Process firefly_akamai cookies coming back from origin, if present.
  if (req.http.Gannett-USAT-Request-CAM == "Enabled") {
    call wally_fetch;
  }

  # preserve Vary headers before stripping and rebuilding with selected Vary headers
  declare local var.preserve_vary STRING;
  set var.preserve_vary = beresp.http.Vary;

  # strip vary from backend response before rebuilding with desired preserved Vary headers
  unset beresp.http.Vary;

  # It's safer to Vary on Cookie now that we're throwing out any unknown cookies
  if (var.preserve_vary ~ "Cookie") {
    set beresp.http.vary:Cookie = "";
  }
  # Accept-Encoding could get set at origin or at the shield (by Fastly gzip boilerplate)
  if (var.preserve_vary ~ "Accept-Encoding") {
    set beresp.http.vary:Accept-Encoding = "";
  }
  # Accept could get set at www.gannett-cdn.com origin
  if (req.http.Gannett-Custom:gcdn) {
    if (var.preserve_vary ~ "Accept") {
      set beresp.http.vary:Accept = "";
    }
  }
  # This is needed for origin/inline roadblocks (CMREV-1890), and will be removed from Vary by WALL*LY when appropriate
  if (var.preserve_vary ~ "Gannett-CAM-Restrict-If") {
    set beresp.http.vary:Gannett-CAM-Restrict-If = "";
  }

  # Set from Tangent origin for page requests that have desktop/mobile variants
  if (var.preserve_vary ~ "Gnt-Mobile") {
    set beresp.http.vary:Gnt-Mobile = "";
  }

  # Set from Tangent origin for in/out market for sports betting
  if (var.preserve_vary ~ "gnt-gm-im") {
    set beresp.http.vary:gnt-gm-im = "";
  }

  # Set from Tangent origin for banner alerts
  if (var.preserve_vary ~ "gnt-ba") {
    set beresp.http.vary:gnt-ba = "";
  }

  # need to vary on req.http.x-origin-expected-host since we never adjust req.http.host
  # and we have static rule that will hash to the same uri
  if (
    # don't create additional cache entries for identical resources across sites
    !req.http.Gannett-Custom:disable-vary-cache
  ) {
    set beresp.http.vary:x-origin-expected-host = "";
  }

  if (
    req.http.Gannett-Custom:site-name == "usatodaysportsplus" &&
    req.url.path == "/"
  ) {
    set beresp.http.vary:gnt-sph = "";
  }

  set beresp.http.vary:gnt-dma = "";

  # Don't create these cache buckets for origins other than UW
  if(req.http.Gannett-Custom:UW) {
    set beresp.http.vary:X-UA-Device = "";
    # Redesigned Nav on UW (PENG-13480)
    set beresp.http.vary:X-NavReskin = "";
  }

  # Set ab test Vary headers.
  if (
    req.http.host ~ "^(?:ci-|staging-|origin-staging-(?:[0-9]-)?|cam-)?www" &&
    req.http.Gannett-Custom:can_run_cookie_logic
  ) {
    call abtest_fetch;
    if (req.http.Gannett-Debug) {
      set beresp.http.Gannett-Debug-Path-Fetch = if(beresp.http.Gannett-Debug-Path-Fetch, beresp.http.Gannett-Debug-Path-Fetch " abtest_fetch", " abtest_fetch");
    }
  }

  # protect from caching empty 200
  if (beresp.status == 200 && beresp.http.Content-Length == "0") {
    return(pass);
  }

  # gannett boilerplate
  # gzip compression
  if ((beresp.status == 200 || beresp.status == 404) && (beresp.http.content-type ~ "^(text\/html|application\/x\-javascript|text\/css|application\/javascript|text\/javascript|application\/json|application\/vnd\.ms\-fontobject|application\/x\-font\-opentype|application\/x\-font\-truetype|application\/x\-font\-ttf|application\/xml|font\/eot|font\/opentype|font\/otf|image\/svg\+xml|image\/vnd\.microsoft\.icon|text\/plain|text\/xml)\s*($|;)" || req.url ~ "\.(css|js|html|eot|ico|otf|ttf|json|svg)($|\?)" ) ) {
    # always set vary to make sure uncompressed versions dont always win
    set beresp.http.vary:Accept-Encoding = "";
    if (req.http.Accept-Encoding == "gzip") {
      set beresp.gzip = true;
    }
  }

  # PAAS-9877
  if ( beresp.status == 301 &&
       (req.url.path == "/news/" ||
       req.url.path == "/sports/" ||
       req.url.path == "/thingstodo/" ||
       req.url.path == "/life/" ||
       req.url.path == "/travel/" ||
       req.url.path == "/opinion/" ||
       req.url.path == "/"
  )){
     /* deliver stale if the object is available */
    if (stale.exists) {
      set req.http.response_info:stale-on-error = "true";
      set beresp.http.stale_from_error = "true";
      return(deliver_stale);
    }

    set beresp.http.Surrogate-Key = "redir-loop";
    set beresp.cacheable = true;

    set beresp.http.Cache-Control = "max-age=10";
    return(deliver);
  }

  /* cache errors for 1m */
  if (beresp.status >= 300 && beresp.status < 600) {
    /* Do not cache SigSci errors */
    if(beresp.http.x-sigsci-agentresponse ~ "(406|429)") {
      return(deliver);
    } elseif(beresp.http.x-sigsci-agentresponse ~ "302") {
      set beresp.ttl = 0s;
      set beresp.cacheable = false;
      return(deliver);
    }

    if (beresp.status >= 400 &&
        beresp.status < 500 &&
        req.url.ext == "" &&
        req.url.path != "/tech/internet/" &&
        req.url.path ~ "^/tech/internet") {

      set req.http.x-Redir-Url = "https://" req.http.host + "/tech/internet/";
      error 701 req.http.x-Redir-Url;
    }

    /* deliver stale if the object is available */
    if (stale.exists) {
      set req.http.response_info:stale-on-error = "true";
      return(deliver_stale);
    }

    if (!req.backend.is_shield) {
      if (req.http.x-origin-expected-host ~ "^tangent-fragments") {
        # serve synthetic response
        if (beresp.status >= 500) {
          error 500;
        } elseif (beresp.status >= 400) {
          error 804;
        }
      } elseif (req.http.x-origin-expected-host == "tangent.usatodaynetworkservice.com") {
        error 804;
      }
    }

    set beresp.http.Surrogate-Key = req.url.dirname " " beresp.status " cache_error";
    set beresp.cacheable = true;

    if (req.http.x-origin-expected-host ~ "storage.googleapis.com"){
      set beresp.ttl = 5s;
    } else {
        if (req.url.path ~ "^/tipico-redirect") {
          set beresp.ttl = 0s;
        } else {
          set beresp.ttl = 20s; # or what ever the TTL should be
        }
    }

    if (req.http.Gannett-Debug) {
      set beresp.http.Gannett-Debug-Path-Fetch = if(beresp.http.Gannett-Debug-Path-Fetch, beresp.http.Gannett-Debug-Path-Fetch " ttl: " beresp.ttl, " ttl: " beresp.ttl);
    }

    return(deliver);
  }

  /* set stale_if_error and stale_while_revalidate (customize these values) */
  set beresp.stale_if_error = 86400s;

  if (req.url.path ~ "/web-sitemap-index.xml" ||
      req.url.path ~ "/news-sitemap.xml" ||
      req.url.path ~ "/video-sitemap-index.xml") {
    set beresp.stale_while_revalidate = 0s;
  } else {
    set beresp.stale_while_revalidate = 60s;
  }


  if ((beresp.status == 500 || beresp.status == 503) && req.restarts < 1 && (req.request == "GET" || req.request == "HEAD")) {
    restart;
  }

  if (req.restarts > 0) {
    set beresp.http.Fastly-Restarts = req.restarts;
  }


  if (beresp.http.Set-Cookie && !req.http.Gannett-Custom:redventures && !req.http.x-origin-expected-host == "gaming.usatoday.com") {
    set req.http.Fastly-Cachetype = "SETCOOKIE";
    return(pass);
  }


  # google and trivia backend is giving a cache-control of private, ignore that
  if (beresp.http.Cache-Control ~ "private" &&
  !req.http.x-origin-expected-host ~ "storage.googleapis.com" &&
  !req.http.x-origin-expected-host == "gaming.usatoday.com" &&
  !req.http.x-origin-expected-host == "celebrity-deaths.usatoday.com" &&
  !req.http.x-origin-expected-host == "most-popular.usatoday.com" &&
  !req.http.Gannett-Custom:booklist &&
  !req.http.Gannett-Custom:elections &&
  !req.http.Gannett-Custom:sportscontests &&
  !req.http.Gannett-Custom:bot-detection &&
  !req.http.Gannett-Custom:celebrityhub &&
  !req.http.Gannett-Custom:petshub) {
    set req.http.Fastly-Cachetype = "PRIVATE";
    return(pass);
  }

  /* this code will never be run, commented out for clarity */
  /* if (beresp.status == 500 || beresp.status == 503) {
     set req.http.Fastly-Cachetype = "ERROR";
     set beresp.ttl = 1s;
     set beresp.grace = 5s;
     return(deliver);
  } */

  if(!req.http.x-append-surrogate){
    set req.http.x-append-surrogate = "";
  }

  # keep TTL that was provided by Tangent backend, other apply TTL rules
  if ((req.http.x-origin-expected-host !~ "^tangent" && req.http.x-origin-expected-host !~ "^usat-(staging|prod).mktplatforms.com") || beresp.http.Surrogate-Control !~ "max-age") {
    # default ttl
    #PAAS-9518 - revert base ttl to 240m until purge is automated
    set beresp.ttl = 240m;

    if (
      req.url.path == "/services/webproxy/" ||
      req.url.path ~ "^/services/breakingnews/" ||
      req.url.path ~ "^/services/breaking-news/" ||
      req.url.path ~ "^/services/markets/" ||
      req.url.path ~ "^/media/latest/" ||
      req.url.path ~ "^/media/popular/" ||
      req.url.path ~ "^/sports/.*/scores" ||
      req.url.path ~ "^/sports/.*/results" ||
      req.url.path ~ "^/sports/.*/schedule" ||
      req.url.path ~ "^/sports/.*/event" ||
      req.url.path ~ "^/sports/.*/leaderboard" ||
      req.url.path ~ "^/sports/services/brackets-boxscore-realtime/" ||
      req.url.path ~ "^/sports/services/brackets-realtime/" ||
      req.url.path ~ "^/sports/services/scores-suspender/" ||
      req.url.path ~ "^/border-wall/api/getPollData" ||
      req.url.path ~ "^/polls/"
    ) {
      set beresp.ttl = 2m;
    } else if (
      req.url.path ~ "^/media/cinematic/gallery/"
    ){
      set beresp.ttl = 5m;
    } else if (
      req.url.path ~ "^/feeds/live/" ||
      req.url.path ~ "^/search/" ||
      req.url.path ~ "^/staff/" ||
      req.url.path ~ "^/weather/overlay/" ||
      req.url.path ~ "^/weather/forecast/" ||
      req.url.path ~ "^/modules/feed-stories/" ||
      req.url.path ~ "^/services/assets/"
    ) {
      set beresp.ttl = 15m;
    } else if (
      req.url.path ~ "^/sports/.*/teams" ||
      req.url.path ~ "^/sports/.*/roster" ||
      req.url.path ~ "^/sports/.*/drivers"
    ){
      set beresp.ttl = 1d;
    } else if (
      req.url.path ~ "^/sports/.*/standings" ||
      req.url.path ~ "^/sports/.*/statistics" ||
      req.url.path ~ "^/sports/.*/transactions" ||
      req.url.path ~ "^/sports/.*/player" ||
      req.url.path ~ "^/sports/.*/rankings" ||
      req.url.path ~ "^/sports/.*/ballots" ||
      req.url.path ~ "^/sports/.*/sagarin" ||
      req.url.path ~ "^/sports/(mlb|nfl|nba|nhl|ncaab|ncaaf)/(\w+)/$"
    ) {
      set beresp.ttl = 60m;
    } else if (
      req.url.path ~ "/20[0123456789]{2}/[0123456789]{2}/" ||
      req.url.path ~ "^/.cam-tangent/asset/"
    ) {
      set beresp.ttl = 30d;
    }
  }

  if (req.http.x-origin-expected-host == "thewall-production.usatoday.com"){
    set beresp.ttl = 365d;
  }

  #celebrity-deaths ttl set
  if( req.http.x-origin-expected-host == "celebrity-deaths.usatoday.com" ){
    set beresp.ttl = 600s;
    set beresp.http.Cache-Control = "300s";
    call keep_origin_cache_control;
  }

  #sports/contests (new experience) ttl set
  if ( req.http.Gannett-Custom:sportscontests ) {
    call gnt_sportscontests_fetch;
  }

  #elections (new experience) ttl set
  if ( req.http.Gannett-Custom:elections ) {
    call gnt_elections_fetch;
  }

  #public notices set x-host and ttl
  if ( req.http.Gannett-Custom:public-notices ) {
    set bereq.http.host = req.http.x-origin-expected-host;
    set bereq.http.x-host = "www." req.http.Gannett-Custom:site-apexdomain;
    set beresp.ttl = 0s;
  }

   #booklist (new experience) ttl set
  if (req.http.Gannett-Custom:booklist == "1") {
    unset beresp.http.etag;
    call gnt_booklist_fetch;
  } else if (req.http.Gannett-Custom:celebrityhub == "1") {
    call gnt_celebrityhub_fetch;
  } else if (req.http.Gannett-Custom:comics == "1") {
    call gnt_comics_fetch;
  } else if (req.http.Gannett-Custom:tailgating == "1") {
    call gnt_tailgating_fetch;
  } else if (req.http.Gannett-Custom:vacation == "1") {
    call gnt_vacation_fetch;
  } else if (req.http.Gannett-Custom:advertisewithus == "1") {
    call gnt_advertise_with_us_fetch;
  } else if (req.http.Gannett-Custom:holiday-marketplace == "1") {
    call gnt_holiday_marketplace_fetch;
  } else if (req.http.Gannett-Custom:home-improvement == "1") {
    call gnt_home_improvement_fetch;
  } else if (req.http.Gannett-Custom:petshub == "1") {
    call gnt_petshub_fetch;
  } else if (req.http.Gannett-Custom:lottery == "1") {
    call gnt_lottery_fetch;
  } else if (req.http.Gannett-Custom:realestate == "1") {
    call gnt_realestate_fetch;
  } else if (req.http.Gannett-Custom:woty == "1") {
    call gnt_woty_fetch;
  } else if (req.http.Gannett-Custom:woty2025 == "1") {
    call gnt_woty2025_fetch;
  } else if (req.http.Gannett-Custom:womenssports == "1") {
    call gnt_womenssports_fetch;
  } else if (req.http.Gannett-Custom:deals == "1") {
    call gnt_deals_fetch;
  } else if (req.http.Gannett-Custom:xpr_media) {
    call gnt_xpr_media_fetch;
  } else if (req.http.Gannett-Custom:sportshubs) {
    call gnt_sports_hubs_fetch;
  } else if (req.http.Gannett-Custom:bot-detection) {
    call gnt_bot_detection_fetch;
  }
  #most-popular ttl set
  if( req.http.x-origin-expected-host == "most-popular.usatoday.com" ){
    set beresp.ttl = 600s;
    set beresp.http.Cache-Control = "300s";
    call keep_origin_cache_control;
  }

  #makestories ttl set
  if( req.http.x-origin-expected-host == "gannett-makestories.storage.googleapis.com" ){
    set beresp.ttl = 1h;
    set beresp.http.Cache-Control = "60s";
  }

  # host based ttl overrides
  if ( req.http.x-origin-expected-host == "www.gannett-cdn.com") {
    set beresp.ttl = 3m;
    if (req.url.path ~ "^/(dcjs|dcc)") {
      set beresp.ttl = 1h;
      set beresp.http.Cache-Control = "max-age=3600";
      if (req.url.path ~ "^/dcjs/.*q1a2z3.*") {
        set beresp.ttl = 10y;
        set beresp.http.Cache-Control = "public,immutable,max-age=315360000";
        set beresp.stale_while_revalidate = 20y;
        set beresp.stale_if_error = 20y;
      }
      call keep_origin_cache_control;
    } else if (req.url.path ~ "^/horoscopes") {
      if (req.url.path ~ "/static/img/") {
        set beresp.ttl = 6h;
        set beresp.http.Cache-Control = "max-age=3600";
        call keep_origin_cache_control;
      } else {
        set beresp.ttl = 1h;
        set beresp.http.Cache-Control = "no-store";
        call keep_origin_cache_control;
      }
    }
  } else if ( req.http.x-origin-expected-host == "content_syndication_sitemap_production.storage.googleapis.com"){
    set beresp.ttl = 3m;
  } else if ( req.http.x-origin-expected-host == "vrstories-www-gannett-cdn-com.storage.googleapis.com" ||
              req.http.x-origin-expected-host == "media.gannett-cdn.com"){
    set beresp.ttl = 30d;
  }

  # shared_proxy_gannett_vcl_fetch_gcdn - req.http.Gannett-Custom:gcdn - setup ttl
  call shared_proxy_gannett_vcl_fetch_gcdn;

  #################
  ### marketing ###
  #################
  if(req.http.host ~ "^(?:ci-|staging-|origin-staging-(?:[0-9]-)?|cam-)?marketing\."){
      set beresp.ttl = 5m;
  }

  ###############
  ### tangent ###
  ###############
  if (req.http.x-origin-expected-host ~ "^tangent") {
    set beresp.stale_while_revalidate = 5m;
  }

  #forbes/blueprint fetch
  if (req.http.x-origin-expected-host ~ "^usat-(staging|prod).mktplatforms.com") {
    call gnt_blueprint_fetch;
  }

  # 301s with non cache-control are dangerous, as chrome and firefox will cache them forever
  # if the backend gave us a cache-control, let that pass, otherwise set one
  if ( beresp.status == 301 ) {
    if ( ! beresp.http.Cache-Control ) {
      set beresp.http.Cache-Control = "max-age=30";
    }
  } elseif (!req.http.Gannett-Custom:keep-origin-cache-control) {
    if (req.url.path ~ "/tangsvc") {
      if (beresp.http.Surrogate-Control !~ "max-age") {
        set beresp.ttl = 7d;
      }
      set beresp.http.Cache-Control = "public, max-age=60";
    } elseif (req.url.path ~ "^/gannett-web/apps/teal/dist/") {
      set beresp.ttl = 30d;
      set beresp.http.Cache-Control = "public, immutable, max-age=315360000";
    } else {
      unset beresp.http.Cache-Control;
    }
  }

  ##### cet-front-end-static pages #####
  if (req.http.x-origin-expected-host == "cet-front-end-static.storage.googleapis.com"){
    set beresp.ttl = 365d;
    if (req.url.ext == "br") {
      set beresp.http.Content-Encoding = "br";
    } elseif (req.url.ext == "gz") {
      set beresp.http.Content-Encoding = "gzip";
    }
    if (req.url.path ~ "\.html") {
      set beresp.http.Content-Type = "text/html; charset=UTF-8";
      # set security policy headers
      set beresp.http.Content-Security-Policy = "upgrade-insecure-requests;frame-ancestors 'none';object-src 'none'";
      set beresp.http.Content-Security-Policy-Report-Only = "script-src https: blob: 'unsafe-inline' 'self';base-uri 'self';report-uri https://reporting-api.gannettinnovation.com";
      set beresp.http.X-Content-Type-Options = "nosniff";
      set beresp.http.X-Frame-Options = "deny";
      set beresp.http.X-XSS-Protection = "1; mode=block";
      set beresp.http.Feature-Policy = "camera 'none';display-capture 'none';geolocation 'none';microphone 'none';payment 'none';usb 'none';xr-spatial-tracking 'none'";
      set beresp.http.Permissions-Policy = "camera=(),display-capture=(),geolocation=(),microphone=(),payment=(),usb=(),xr-spatial-tracking=()";
      set beresp.http.Referrer-Policy = "strict-origin-when-cross-origin";
      set beresp.http.Cross-Origin-Resource-Policy = "same-origin";
    } elseif (req.url.path ~ "\.js") {
      set beresp.http.Cache-Control = "public, immutable, max-age=315360000";
      # set security policy headers
      set beresp.http.X-Content-Type-Options = "nosniff";
    }
  }

  # /betting gambling route
  if (req.http.x-origin-expected-host ~ "\.usatoday-gdcgroup.com"){
    call gnt_betting_fetch;
  }

  unset beresp.http.Expires;

  if (req.http.Gannett-Debug) {
    set beresp.http.Gannett-Debug-Path-Fetch = if(beresp.http.Gannett-Debug-Path-Fetch, beresp.http.Gannett-Debug-Path-Fetch " ttl: " beresp.ttl, " ttl: " beresp.ttl);
  }

  return(deliver);
}

sub vcl_hit {
#FASTLY hit
  if (!obj.cacheable) {
    return(pass);
  }

  if (!obj.cacheable) {
    return(pass);
  }
  return(deliver);
}

sub vcl_miss {
  # shared_helpers_frontend - set log_timing header
  call shared_helpers_frontend_log_timing_misspass;

  #Proxy client host header for UW requests
  if(req.http.Gannett-Custom:UW == "1"){
    set req.http.x-origin-expected-host = req.http.host;
  }

  call shared_helpers_general_set_hostheader_for_origin;
  set bereq.http.Gannett-Debug-Path = req.http.Gannett-Debug-Path " --->";
#FASTLY miss
  if (bereq.http.X-Forwarded-Host ~ "^([^,]+),[^,]*.*$") {
    set bereq.http.X-Forwarded-Host = re.group.1;
  }

  if (req.http.gannett-custom:x-block-request == "true") {
     error 403 "Forbidden";
  }

  # strip certain headers from going to origin
  if (req.http.Gannett-Custom:UW) {
    unset bereq.http.X-Forwarded-Host;
  }
  unset req.http.x-original-url;
  unset req.http.x-rewrite-url;
  if (req.backend.is_origin) {
    call shared_helpers_general_strip_client_ip_from_bereq;
    unset bereq.http.vcl_data;
    unset bereq.http.Gannett-FF;
    unset bereq.http.Gannett-FF-HMAC;
    unset bereq.http.Fastly-Cachetype;
    unset bereq.http.Fastly-Debug-Digest;
    unset bereq.http.Fastly-Debug-Path;
    unset bereq.http.Fastly-Debug-TTL;
    unset bereq.http.Fastly-Debug;
    if (
      req.http.x-origin-expected-host ~ "^tangent" ||
      req.http.x-origin-expected-host ~ "^usat-(staging|prod).mktplatforms.com"
    ) {
      call clean_backend_request_headers;
    } else if (req.http.Gannett-Custom:UW) { #UW depends on a req header to determine if the request came from a bot
      unset bereq.http.cookie;
      unset bereq.http.GUP-Identifiers;
    }
  }

  # shared_proxy_gannett_vcl_miss_gcdn - req.http.Gannett-Custom:gcdn - clean request headers
  call shared_proxy_gannett_vcl_miss_gcdn;

  return(fetch);
}

sub vcl_hash {

  #sports/contests
  if (req.http.Gannett-Custom:sportscontests) {
    call gnt_sportscontests_hash;
  }

  #elections
  if (req.http.Gannett-Custom:elections) {
    call gnt_elections_hash;
  }

  #booklist
  if (req.http.Gannett-Custom:booklist) {
    call gnt_booklist_hash;
  }

  if (req.url.qs ~ "^utm_source=taboola" || req.url.qs ~ "&utm_source=taboola") {
    set req.hash += querystring.filter(req.url, "utm_source" + querystring.filtersep() + "utm_medium" querystring.filtersep() + "utm_campaign");
  } else {
    # convert gnt-mobile QSP to header to continue supporting it for manual testing on Tangent only and ensure its stripped for other platforms
    if (req.url.qs ~ "gnt-mobile") {
      if (req.http.x-origin-expected-host ~ "^tangent") {
        call shared_tangent_set_mobile;
      } else {
        set req.url = querystring.filter(req.url, "gnt-mobile");
      }
    }
    if (req.http.x-origin-expected-host ~ "^tangent") {
      # prepend additional query string params to default whitelist based on path
      if (req.url.path ~ "^/search" || req.url.path ~ "^/recalls") {
        set req.url = querystring.regfilter_except(req.url, "q|page|tangent|gnt-debug|gnt-unclean|gnt-basket");
        if (req.url.qs ~ "^page=1" || req.url.qs ~ "&page=1" || (req.url.path ~ "^/search" && req.url.qs !~ "^q=" && req.url.qs !~ "&q=")) {
          set req.http.Gannett-Debug-Path-Item = "search strip_page";
          call shared_helpers_general_record_object_path;
          set req.url = querystring.regfilter(req.url, "page"); # limit cached entries for 1st page of search results since ?q=term&page=1 == ?q=term
        }
      } elseif (req.url.path ~ "^/tangsvc/pg/") {
        set req.url = querystring.regfilter_except(req.url, "ids|tangent|gnt-debug|gnt-unclean|gnt-basket");
      } elseif (req.url.path ~ "^/tangfrag/") {
        set req.url = querystring.regfilter_except(req.url, "tangent|gnt-debug|gnt-unclean|gnt-basket|prm-");
      } elseif (req.url.path ~ "^/story/") { # per inline roadblock
        set req.url = querystring.regfilter_except(req.url, "tangent|gnt-debug|gnt-unclean|gnt-basket|gnt-cfr");
        if (req.url.qs ~ "gnt-cfr") { # force set consistent value
          set req.url = querystring.set(req.url, "gnt-cfr", "1");
        }
      } else {
        # default tangent qsp whitelist
        set req.url = querystring.regfilter_except(req.url, "tangent|gnt-debug|gnt-unclean|gnt-basket");
      }
    }
    set req.url = boltsort.sort(req.url);
    set req.hash += req.url;
  }
  if (
    (req.http.x-origin-expected-host !~ "^tangent" || req.url.path !~ "^/tangstatic/") &&
    !req.http.Gannett-Custom:disable-vary-cache
  ) {
    set req.hash += req.http.host;
  }
  set req.hash += req.vcl.generation;
  return(hash);

#FASTLY hash
}

sub vcl_deliver {
  if (req.http.Gannett-Debug) {
    if (resp.http.Gannett-Debug-Path-Fetch) {
      set resp.http.Gannett-Debug-Path = req.http.Gannett-Debug-Path "; " resp.http.Gannett-Debug-Path-Fetch;
    } else {
      set resp.http.Gannett-Debug-Path = req.http.Gannett-Debug-Path;
    }
    set resp.http.Gannett-Debug-Path-Full = if(resp.http.Gannett-Debug-Path-Full, " restarts: " req.restarts " shield: " if(req.backend.is_shield, "true", "false") " server: " server.identity " state: " fastly_info.state " path: " resp.http.Gannett-Debug-Path " >>>> " resp.http.Gannett-Debug-Path-Full, " restarts: " req.restarts " shield: " if(req.backend.is_shield, "true", "false") " server: " server.identity " state: " fastly_info.state " path: " resp.http.Gannett-Debug-Path);
  }

  # shared_helpers_frontend - set log_timing header
  call shared_helpers_frontend_log_timing_deliver;

  # prebid - don't override vcl_deliver logic from prebid service
  call shared_prebid_deliver;

  # shared_proxy_vendor_deliver - return(deliver) - early exit from vcl_deliver to avoid override of vcl_deliver logic from reverse-proxy-vcl service
  call shared_proxy_vendor_deliver;

  # shared_proxy_gannett_vcl_pass_gcias - req.http.x-origin-expected-host ~ "gcias-compute.usatoday.com" - return(deliver)
  call shared_proxy_gannett_vcl_deliver_gcias;

  # On non-shield nodes only for HTML pages
  if (
    fastly.ff.visits_this_service == 0 &&
    std.prefixof(resp.http.Content-Type, "text/html") &&
    req.http.host !~ "usatodaynetworkservice\.com$" &&
    req.url.path !~ "^/tangfrag/"
  ) {
    # ensure GUP cookies are set on all successful responses and 404/500 error responses for tangent & UW
    if (
      resp.status == 200 ||
      (
        (resp.status == 404 || resp.status == 500) && req.http.x-origin-expected-host ~ "^(tangent|uw)\."
      )
    ) {
      call guplib_deliver_set_missing_id_cookies;
    }
    # ensure GUP preload link header is set on candidate responses
    call shared_helpers_frontend_usat_uscp_set_gup_user_data_preload_link;
  }

  # Tangent overrides at edge pop
  call tangent_deliver_edge_overrides;

  # See if something has signaled that this request should vary on User-Agent
  if (
    (req.http.vary_on_ua_for_cam == "Yes" && resp.http.X-Content-Access-Type) ||
    (
      std.prefixof(resp.http.Content-Type, "text/html") &&
      req.http.host !~ "usatodaynetworkservice\.com$"
    ) ||
    req.http.Gannett-Custom:vary_on_ua ||
    (
      resp.status > 300 && resp.status < 304
    )
  ) {
    set resp.http.Vary:User-Agent = "";
  }
  unset req.http.vary_on_ua_for_cam;

  # vary on Sec-CH-UA-Mobile
  if (
    req.http.x-origin-expected-host ~ "^tangent" &&
    resp.http.Vary ~ "(?i)gnt-mobile"
  ) {
    set resp.http.Vary:Sec-CH-UA-Mobile = "";
  }

  # Capture Skey in req header to be used in vcl_log
  set req.http.Surrogate-Key = resp.http.Surrogate-Key;
  set req.http.response_info:backend_ip = resp.http.backend_ip;
  unset resp.http.backend_ip;
  unset resp.http.vcl_data;

  if (req.http.host ~ "^(?:ci-|staging-|origin-staging-(?:[0-9]-)?|cam-)") {
    set resp.http.X-Robots-Tag = "noindex, nofollow";
  }

  if (resp.status >= 500 && resp.status < 600) {
    /* restart if the stale object is available */
    if (stale.exists) {
      restart;
    }
  }

  #ORD-5760 error page restarts
  if (fastly.ff.visits_this_service == 0) {
    if (req.http.Gannett-Custom:UW) {
      if (
        resp.status < 400 &&
        resp.http.location ~ "usatoday30\.usatoday\.com"
      ) {
        unset req.http.Gannett-Custom:UW;
        unset req.http.x-append-surrogate;

        set req.url = "/errors/404/";
        call error_page_restart_deliver;
      }
    } elseif (req.http.x-origin-expected-host == "tangent.usatoday.com") {
      if (req.url.path == "/errors/404/") {
        set resp.status = 404;
      } elseif (req.url.path == "/errors/500/") {
        set resp.status = 500;
      } else {
        call error_page_restart_deliver_check;
      }
    } elseif (req.http.x-origin-expected-host == "cet-front-end-static.storage.googleapis.com"){
      call error_page_restart_deliver_check;
    } elseif (req.http.x-origin-expected-host == "gannett-makestories.storage.googleapis.com"){
      call error_page_restart_deliver_check;
    }
  }

  # WALL*LY: Make sure user is allowed to see this content.
  # Can throw faux error status 801.
  if (req.http.Gannett-USAT-Request-CAM == "Enabled") {
    call wally_deliver;
    if (req.http.Gannett-Debug) {
      set resp.http.Gannett-Debug-Path = resp.http.Gannett-Debug-Path " ; "  "wally_deliver";
    }
  }

  set req.http.Gannett-Custom:req-header-count = std.count(req.headers) "." req.restarts;
  # Always run cleanup after wally_deliver when on edge
  if (fastly.ff.visits_this_service < 1) {
    call wally_cleanup;
  }

  if (!req.http.Gannett-FF || req.http.Gannett-FF == server.identity) {
    # Set gnt_region cookie for every visitor if not set and zip code matches.
    # Pulls the user's geolocation and sets values accordingly.
    if (req.http.gannett-geo-ip-override) {
        set client.geo.ip_override = req.http.gannett-geo-ip-override;
        unset req.http.gannett-geo-ip-override;
      } else {
        set client.geo.ip_override = req.http.Fastly-Client-IP;
    }

    # set header to be used to prevent cookie logic from running on subresources
    call shared_helpers_tangent_can_run_cookie_logic_deliver;

    if (req.http.Gannett-Custom:can_run_cookie_logic) {

      # allows for a zip header override for testing
      declare local var.gnt_zip_lookup STRING;
      declare local var.gnt_zip_sitedata STRING;
      declare local var.gnt_region_value STRING;
      set var.gnt_zip_lookup = querystring.get(req.url, "gnt-zip");
      if (std.strlen(var.gnt_zip_lookup) == 0) {
        set var.gnt_zip_lookup = if (req.http.gannett-geo-zip-override, req.http.gannett-geo-zip-override, client.geo.postal_code);
      }

      set var.gnt_zip_sitedata = table.lookup(gnt_zip_east, var.gnt_zip_lookup, table.lookup(gnt_zip_west, var.gnt_zip_lookup, false));
      if(var.gnt_zip_sitedata != false){
        set var.gnt_region_value = digest.base64(table.lookup(gnt_zip_sitedata, var.gnt_zip_sitedata, ""));
      }
      if(std.strlen(var.gnt_region_value) > 0 && var.gnt_region_value != req.http.Cookie:gnt_region){
        add resp.http.Set-Cookie = "gnt_region=" var.gnt_region_value ";domain=." req.http.Gannett-Custom:site-apexdomain ";path=/;secure;samesite=lax;max-age=21600;";
      } elseif(req.http.Cookie:gnt_region) {
        add resp.http.Set-Cookie = "gnt_region=deleted;domain=." req.http.Gannett-Custom:site-apexdomain ";expires=Thu, 01 Jan 1970 00:00:01 GMT;Max-Age=0;path=/;";
      }

      # Set a/b cookies and response headers.
      if (
        req.http.host ~ "^(?:ci-|staging-|origin-staging-(?:[0-9]-)?|cam-)?www"
      ) {
        call abtest_deliver;
        if (req.http.Gannett-Debug) {
          set resp.http.Gannett-Debug-Path = resp.http.Gannett-Debug-Path " ; "  "abtest_deliver";
        }
      }

      # setting client-side response cookie and response header for the fastly geo/CAM market workflow
      if (req.http.Gannett-Cam-Experience-Id:existing_gnt_eid != req.http.Gannett-Cam-Experience-Id:segment) {
        add resp.http.Set-Cookie = "gnt_eid=" req.http.Gannett-Cam-Experience-Id:segment ";domain=." req.http.Gannett-Custom:site-apexdomain ";path=/;secure;samesite=lax;max-age=5184000;priority=high;";
        if (req.http.Gannett-Custom:UW == "1" || req.http.x-origin-expected-host == "universal-web") {
          add resp.http.Gannett-Cam-Experience-Id = req.http.Gannett-Cam-Experience-Id:segment;
        }
      } else {
        if (req.http.Gannett-Debug) {
          set resp.http.Gannett-Debug-Path = resp.http.Gannett-Debug-Path " ; "  "not setting gnt_eid";
        }
      }
      if (req.http.Gannett-Cam-Experience-Id:cam_eid_override) {
        add resp.http.Set-Cookie = "cam_eid_override=" req.http.Gannett-Cam-Experience-Id:cam_eid_override ";domain=." req.http.Gannett-Custom:site-apexdomain ";path=/;secure;samesite=lax;";
      }

      # cookie to req object so it can be added to server_timing header later
      call shared_helpers_frontend_gnt_bot_cookie;

      if (req.http.x-origin-expected-host ~ "^tangent\.") {
         if (req.http.Gannett-Custom:gnt-w) {
           add resp.http.Set-Cookie = "gnt_w=" req.http.Gannett-Custom:gnt-w ";domain=" req.http.host ";path=/;samesite=lax;secure;priority=high;";
         }
         call set_weather_resp_link;
       }

      # gnt_i
      call shared_helpers_frontend_set_cookie_gnt_i;

      # gnt_ti - taboola userID
      call shared_tangent_set_cookie_gnt_ti;

	    # _ga - GA clientID
      call shared_tangent_set_cookie__ga;

      # remove deprecated gnt_d cookie
      if (req.http.Cookie:gnt_d) {
        add resp.http.Set-Cookie = "gnt_d=del;max-age=-1;path=/;domain=" req.http.Gannett-Custom:site-apexdomain;
      }
    }

    # Set Network Error Logging (NEL) and Reporting API headers
    # Add JS call stacks in crash reports policy if not already present
    call shared_helpers_general_deliver_set_reporting_api_headers;
  } else {
    set req.http.Gannett-Custom:req-header-count = std.count(req.headers) "." req.restarts;
  }

#FASTLY deliver

  # Hide the existence of the header from downstream
  if (resp.http.Vary && !req.http.Fastly-FF) {
    set resp.http.Vary = regsub(resp.http.Vary, "x-origin-expected-host,?\s?", "");
    set resp.http.Vary = regsub(resp.http.Vary, "X-AbVariant,?\s?", "");
    set resp.http.Vary = regsub(resp.http.Vary, "X-AbVCfg,?\s?", "");
    set resp.http.Vary = regsub(resp.http.Vary, "gnt-dma,?\s?", "");
    if(req.http.Gannett-Custom:UW) {
      set resp.http.Vary = regsub(resp.http.Vary, "X-UA-Device,?\s?", "");
      set resp.http.Vary = regsub(resp.http.Vary, "X-NavReskin,?\s?" , "");
    } elseif (req.http.x-origin-expected-host ~ "^tangent") {
      set resp.http.Vary = regsub(resp.http.Vary, "Gnt-Mobile,?\s?", "");
      set resp.http.Vary = regsub(resp.http.Vary, "gnt-gm-im,?\s?", "");
      set resp.http.Vary = regsub(resp.http.Vary, "gnt-sph,?\s?", "");
      set resp.http.Vary = regsub(resp.http.Vary, "gnt-ba,?\s?", "");
    }

    set resp.http.Vary = regsub(resp.http.Vary, ",\s?$", "");
  }

  set resp.http.Gannett-Debug-Version = table.lookup(buildinfo, "version", "0.0.0");

  # /betting gambling deliver
  if (req.http.x-origin-expected-host ~ "\.usatoday-gdcgroup.com"){
    call gnt_betting_deliver;
  }

  if (req.http.Gannett-Custom:deals == "1" ){ # /deals backend logic
    call gnt_deals_deliver;
  }

  if (req.http.x-origin-expected-host ~ "^usat-(staging|prod).mktplatforms.com"){
    call gnt_blueprint_deliver;
  }

  # Remove unwanted response headers from origin / Fastly
  if (req.http.Gannett-Debug) {
    set resp.http.Server-Timing = if (resp.http.Server-Timing, resp.http.Server-Timing ",", "") fastly_info.state {", fastly;desc="Edge time";dur="} time.elapsed.msec;
  } else {
    # Gannett Custom VCL
    unset resp.http.Gannett-Debug-Path-Full;
    unset resp.http.Gannett-Debug-Path;
    unset resp.http.Gannett-Debug-Version;
    unset resp.http.X-AbVariant;
    # Fastly
    unset resp.http.Expires;
    unset resp.http.Pragma;
    unset resp.http.X-Cache-Hits;
    unset resp.http.X-Served-By;
    unset resp.http.X-Timer;
    unset resp.http.Via;
    # Origin
    unset resp.http.Server;
    unset resp.http.X-Powered-By;
    unset resp.http.X-Generator;
    if (req.http.x-origin-expected-host == "cet-front-end-static.storage.googleapis.com") {
      unset resp.http.x-goog-hash;
      unset resp.http.x-goog-storage-class;
      unset resp.http.x-guploader-uploadid;
      unset resp.http.x-amz-checksum-crc32c;
      unset resp.http.x-goog-generation;
      unset resp.http.x-goog-metageneration;
      unset resp.http.x-goog-stored-content-encoding;
      unset resp.http.x-goog-stored-content-length;
    }
    call shared_tangent_unset_origin_resp_headers;
  }

  if ( req.http.Gannett-Custom:xpr_media ) {
    # xpr media
    call gnt_xpr_media_deliver;
  } elseif ( req.http.Gannett-Custom:elections ) {
    #elections (new experience) deliver
    call gnt_elections_deliver;
  } elseif ( req.http.Gannett-Custom:sportscontests ) {
    #sportscontests (new experience) deliver
    call gnt_sportscontests_deliver;
  } elseif (req.http.Gannett-Custom:celebrityhub) {
    #celebrityhub
    call gnt_celebrityhub_deliver;
  } elseif (req.http.Gannett-Custom:petshub) {
    #petshub
    call gnt_petshub_deliver;
  } elseif (req.http.Gannett-Custom:sportshubs) {
    #SportsHubs
    call gnt_sports_hubs_deliver;
  }

  # remove COOP resp header for Safari based browsers to prevent blank 304 responses
  if (
    (
      req.http.x-origin-expected-host ~ "^tangent" ||
      req.http.x-origin-expected-host == "cet-front-end-static.storage.googleapis.com"
    ) &&
    (
      req.http.Gannett-Browser:Name == "safari" ||
      req.http.Gannett-OS:Name == "iphone" ||
      req.http.Gannett-OS:Name == "ipad"
    )
  ) {
    unset resp.http.Cross-Origin-Opener-Policy;
  }

  # keep Tangent or elsewhere provided header value
  if (req.http.x-origin-expected-host !~ "^tangent" || !resp.http.Content-Security-Policy) {
    # and keep from error 803 synthetic response
    if (!req.http.gnt-client:unsupported-browser && resp.status != 403) {
      # header offers an indicator to browsers to automatically issue HTTPS requests where HTTP links have been used on the page
      set resp.http.Content-Security-Policy = "upgrade-insecure-requests";
    }
  }

  #dont set the HSTS header for non-http requests
  if (req.protocol == "https") {
    # force set hsts header for all www requests https://www.owasp.org/index.php/HTTP_Strict_Transport_Security_Cheat_Sheet
    if (req.http.host ~ "usatodaynetworkservice\.com$" || req.http.host ~ "usatodaysportsplus\.com$") {
      set resp.http.Strict-Transport-Security = "max-age=63072000; includeSubDomains; preload";
    } elseif (req.http.host ~ "^(?:ci-|staging-|origin-staging-(?:[0-9]-)?|cam-)?(?:www|usatoday)") {
      set resp.http.Strict-Transport-Security = "max-age=63072000";
    }
  }

  if (req.url.path ~ "/web-sitemap-index.xml" ||
      req.url.path ~ "/news-sitemap.xml" ||
      req.url.path ~ "/video-sitemap-index.xml") {
        unset resp.http.Cache-Control;
  }

  if (fastly.ff.visits_this_service == 0) {
    # ensure page level responses and redirects are not stored at shared proxy caches and
    # client checks with server that cached copy is still valid
    call shared_helpers_frontend_cache_control_overrides;

    #rec-ai set cache-control
    call shared_critical_vcl_deliver_rec_ai;

    # copy to server_timing header as backup for when cookies are disabled after cache-control has been set
    call shared_helpers_frontend_copy_gnt_i_server_timing;
    call shared_helpers_frontend_copy_gnt_bot_server_timing;

    call shared_tangent_back_button_banner_hub_deliver;

    # shared_proxy_gannett_vcl_deliver_gcdn - req.http.Gannett-Custom:gcdn - unset cookie
    call shared_proxy_gannett_vcl_deliver_gcdn;

    # shared_helpers_frontend - sets client.socket.cwnd size and congestion_algorithm = bbr on the client request
    call shared_helpers_frontend_set_congestion_window;
  }

  if (req.http.Gannett-Debug) {
    if (resp.http.Gannett-Debug-Path-Full ~ "(.*) >>>> (.*)") {
      set resp.http.Gannett-Debug-Path-Full = " restarts: " req.restarts " shield: " if(req.backend.is_shield, "true", "false") " server: " server.identity " state: " fastly_info.state " path: " resp.http.Gannett-Debug-Path " >>>> " re.group.2;
    } else {
      set resp.http.Gannett-Debug-Path-Full = " restarts: " req.restarts " shield: " if(req.backend.is_shield, "true", "false") " server: " server.identity " state: " fastly_info.state " path: " resp.http.Gannett-Debug-Path;
    }
  }

  return(deliver);
}

sub vcl_error {
#FASTLY error

  if (obj.status == 610) {
    call backend_healthchecks;
  }

  # process rules to serve synthetic reponses
  call shared_helpers_front_end_process_synthetic_rules_error;

  if (obj.status == 971) {
    set obj.http.Content-Type = "text/html; charset=utf-8";
    set obj.http.WWW-Authenticate = "Basic realm=Secured";
    set obj.status = 401;
    synthetic {"<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
    "http://www.w3.org/TR/1999/REC-html401-19991224/loose.dtd">
    <HTML>
    <HEAD>
    <TITLE>Error</TITLE>
    <META HTTP-EQUIV='Content-Type' CONTENT='text/html;'>
    </HEAD>
    <BODY><H1>401 Unauthorized</H1></BODY>
    </HTML>
    "};
    return (deliver);
  }

  if (obj.status == 629) {
    set obj.status = 429;
    set obj.response = "Rate Limit Exceeded";
    synthetic "Too many requests";
    return(deliver);
  }

  # gannett boilerplate
  if (obj.status == 701) {
    set obj.http.Location = req.http.x-Redir-Url;
    set obj.status = 301;
    set obj.http.Cache-Control = "max-age=300";
    return(deliver);
  }

  if (obj.status == 702) {
    set obj.http.Location = req.http.x-Redir-Url;
    set obj.status = 302;
    set obj.http.Cache-Control = "private,no-cache,no-store";
    return(deliver);
  }

  if (obj.status == 800) {
    set obj.status = 400;
    set req.http.synthetic = "true";
    synthetic {"<!DOCTYPE html><html>Bad Request</html>"};
    return(deliver);
  }

  if (obj.status == 803 && req.http.gnt-client:unsupported-browser) {
    if (req.url.path ~ "^/tangfrag/") {
      call shared_tangent_supported_browser_synthethic_tangfrag;
    } elseif (req.url.path ~ "^/services/cobrand/") {
      call shared_helpers_frontend_supported_browser_synthethic_gannett_cobrand;
    } else {
      call shared_helpers_frontend_supported_browser_synthethic_gannett_default;
    }
  }

  if (obj.status == 804) {
    set obj.status = 404;
    set req.http.synthetic = "true";
    synthetic {"<!DOCTYPE html><html>Not Found</html>"};
    return(deliver);
  }

  if (obj.status == 810) {
    set obj.status = 410;
    set req.http.synthetic = "true";
    synthetic {"<!DOCTYPE html><html>Gone</html>"};
    return(deliver);
  }

  # WALL*LY: Handle redirects to roadblock pages.  Responds to faux status 801.
  if (req.http.Gannett-USAT-Request-CAM == "Enabled") {
    call wally_error;
  } else {
    # So that the WALL*LY synthetic resources can still be served when disabled.
    call wally_error_url_routing;
  }

  /* handle 503s */
  if (obj.status >= 500 && obj.status < 600) {

    /* deliver stale object if it is available */
    if (stale.exists) {
      set req.http.response_info:stale-on-error = "true";
      return(deliver_stale);
    }

    set req.http.response_info:synthetic = "true";
    /* otherwise, return a synthetic */
    synthetic {"<!DOCTYPE html><html>The Site is down for maintenance.</html>"};
    return(deliver);
  }
}

sub vcl_pass {
  # shared_helpers_frontend - set log_timing header
  call shared_helpers_frontend_log_timing_misspass;

  #Proxy client host header for UW requests
  if(req.http.Gannett-Custom:UW == "1"){
    set req.http.x-origin-expected-host = req.http.host;
  }

  call shared_helpers_general_set_hostheader_for_origin;
  set bereq.http.Gannett-Debug-Path = req.http.Gannett-Debug-Path " --->";

  #public notices set x-host and ttl
  if ( req.http.Gannett-Custom:public-notices ) {
    set bereq.http.host = req.http.x-origin-expected-host;
    set bereq.http.x-host = "www." req.http.Gannett-Custom:site-apexdomain;
  }

#FASTLY pass
  if (bereq.http.X-Forwarded-Host ~ "^([^,]+),[^,]*.*$") {
    set bereq.http.X-Forwarded-Host = re.group.1;
  }

  # strip certain headers from going to origin
  if (req.http.Gannett-Custom:UW ) {
    unset bereq.http.X-Forwarded-Host;
  }
  # unset req.http.x-original-url;
  # unset req.http.x-rewrite-url;
  # if (req.backend.is_origin) {
  #   call shared_helpers_general_strip_client_ip_from_bereq;
  #   unset bereq.http.vcl_data;
  #   unset bereq.http.Gannett-FF;
  #   unset bereq.http.Gannett-FF-HMAC;
  #   unset bereq.http.Fastly-Cachetype;
  #   unset bereq.http.Fastly-Debug-Digest;
  #   unset bereq.http.Fastly-Debug-Path;
  #   unset bereq.http.Fastly-Debug-TTL;
  #   unset bereq.http.Fastly-Debug;
  #   if (
  #     req.http.x-origin-expected-host ~ "^tangent" ||
  #     req.http.x-origin-expected-host ~ "^usat-(staging|prod).mktplatforms.com"
  #   ) {
  #     call clean_backend_request_headers;
  #   } else if (req.http.Gannett-Custom:UW) { #UW depends on a req header to determine if the request came from a bot
  #     unset bereq.http.cookie;
  #     unset bereq.http.GUP-Identifiers;
  #   }
  # }

  # shared_proxy_gannett_vcl_pass_gcdn - req.http.Gannett-Custom:gcdn - return(fetch) - clean request headers
  call shared_proxy_gannett_vcl_pass_gcdn;
  # shared_proxy_gannett_vcl_pass_gcias - req.http.x-origin-expected-host ~ "gcias-compute.usatoday.com" - return(fetch) - set x-forwarded-for header
  call shared_proxy_gannett_vcl_pass_gcias;

  unset req.http.x-original-url;
  unset req.http.x-rewrite-url;
}

sub vcl_log {
  if (req.http.User-Agent !~ "(?i)google" && randombool(1,400)) {
    # shared_helpers_frontend - set log_timing headers
    call shared_helpers_frontend_log_timing_log;
    set req.http.log-timing:log = "";
  } else if (req.http.User-Agent ~ "(?i)googlebot") {
    # temporatily enable 100% google bot logs
    call log_big_query;
  }

#FASTLY log

  # log all SigSci 302 generated by AI Bots
  if(req.url.path ~ "^/bot-detection" || req.http.x-sigsci-agentresponse == "302"){
    # percent of  errors get logged
    if ( randombool(std.atoi(table.lookup(logging_rate, "logging_percent_of_other_errors")),100)) {
      call log_big_query;
    }
  } elseif (resp.status >= 400 && resp.status < 600) {
    # percent of  errors get logged
    if ( randombool(std.atoi(table.lookup(logging_rate, "logging_percent_of_fail")),100)) { # log % of fail
      call log_big_query;
      call general_log_bucket;
    }
  } elseif (req.http.log-timing:log) {
    # shared_helpers_frontend - log connection info for small % of requests for congestion window testing
    # call shared_helpers_frontend_log_connection_info;
  } else if ( req.http.Gannett-Debug == "gnt-log" || randombool(std.atoi(table.lookup(logging_rate, "logging_percent_of_success")),100) ) {
    call log_big_query;
    call general_log_bucket;
  }
  #Tollbit logging
  log {"syslog"} req.service_id {" tollbit-prod :: "};
}

# Figure out whether CAM should be enabled for this request
sub determine_cam_enabled {
  # Default to disabled
  set req.http.Gannett-USAT-Request-CAM = table.lookup(usat_cam_settings, "default_request_restrictions", "Disabled");

  # Since this subroutine will also run on shield, default to this being off
  set req.http.vary_on_ua_for_cam = "No";

  # Should only be enabled when acting as an edge
  if (fastly.ff.visits_this_service > 0) {
    set req.http.Gannett-USAT-Request-CAM = "Disabled (Not Edge)";
  }

  # Should be disabled if coming from authorized IP
  if (req.http.gannett-geo-ip-override && req.http.Gannett-Debug) {
    set req.http.Fastly-Client-IP = req.http.gannett-geo-ip-override;
  }
  if (table.lookup(cam_disabled_ip, req.http.Fastly-Client-IP, "false") == "disabled") {
  # if (table.lookup(cam_disabled_ip, req.http.Fastly-Client-IP)) {
    set req.http.Gannett-USAT-Request-CAM = "Disabled (by IP)";
    if (req.http.Gannett-Debug) {
      set req.http.Gannett-Debug-Path-Item = req.http.Fastly-Client-IP ": Cam Disabled";
      call shared_helpers_general_record_object_path;
    }
  }

  # Leave this test for last because it'll mean we're varying on User-Agent
  if (req.http.Gannett-USAT-Request-CAM == "Enabled") {
    # Always allow traffic from these search engines / bots through
    if (req.http.User-Agent ~ "(?i)Twitterbot|\.taboola\.|bing\.|Yahoo\.|Embedly\.|outbrain\.|google\.|AdsBot-Google\.|Googlebot\.|Yahoo!\.|MSNBot\.|Yahoo-Newscrawler\.|Slurp\.|UltraSeek\.|ArchitextSpider\.|WebCrawler\.|Googlebot|Googlebot-News|Googlebot-Image|Googlebot-Video|Googlebot-Mobile|Mediapartners-Google|Mediapartners|AdsBot-Google|bingbot|msnbot|adidxbot|\.linkedin\.com|cXensebot|facebook|FBFor|twitter|SiteArc|Screaming Frog SEO Spider|rogerbot|dotbot|Social News Desk|parse\.ly") {
      set req.http.Gannett-USAT-Request-CAM = "Disabled (Bot User-Agent)";

      # We hope to switch to using only IP-based allows for several crawlers currently allowed through via User-Agent only.
      # This will allow us to conditionally log cases that would be blocked for monitoring before we actually enforce IPs.
      if (
        # See if this looks like someone pretending to be Google
        req.http.User-Agent ~ "Googlebot|Googlebot-News|Googlebot-Image|Googlebot-Video|Googlebot-Mobile|AdsBot-Google\.|Googlebot\.|google\.|AdsBot-Google"
        && !(client.ip ~ cam_bot_google_bot_ips || client.ip ~ cam_bot_google_ips)
      ) {
        # Conditionally log a percentage of these cases
        if (req.http.Gannett-Debug == "gnt-log" || randombool(std.atoi(table.lookup(wally_dynamic_settings, "cam_crawler_test_logging_percent_google", "0")), 100) ) {
          set req.http.wally_log_event = "Crawler Fake - Google";
          call wally_log_bigquery;
        }
      } elsif (
        # See if this looks like someone pretending to be Bing
        req.http.User-Agent ~ "bing\.|MSNBot\.|bingbot|msnbot"
        && !(client.ip ~ cam_bot_bing_ips)
      ) {
        # Conditionally log a percentage of these cases
        if (req.http.Gannett-Debug == "gnt-log" || randombool(std.atoi(table.lookup(wally_dynamic_settings, "cam_crawler_test_logging_percent_bing", "0")), 100) ) {
          set req.http.wally_log_event = "Crawler Fake - Bing";
          call wally_log_bigquery;
        }
      } elsif (
        # See if this looks like someone pretending to be Facebook
        req.http.User-Agent ~ "facebook|FBFor"
        && !(client.as.number == table.lookup_integer(cam_bot_asns, "facebook", -1))
      ) {
        # Conditionally log a percentage of these cases
        if (req.http.Gannett-Debug == "gnt-log" || randombool(std.atoi(table.lookup(wally_dynamic_settings, "cam_crawler_test_logging_percent_facebook", "0")), 100) ) {
          set req.http.wally_log_event = "Crawler Fake - Facebook";
          call wally_log_bigquery;
        }
      } elsif (
        # See if this looks like someone pretending to be X ~Twitter~
        req.http.User-Agent ~ "Twitterbot|twitter"
        && !(client.as.number == table.lookup_integer(cam_bot_asns, "twitter", -1))
      ) {
        # Conditionally log a percentage of these cases
        if (randombool(std.atoi(table.lookup(wally_dynamic_settings, "cam_crawler_test_logging_percent_twitter", "0")), 100) ) {
          set req.http.wally_log_event = "Crawler Fake - Twitter";
          call wally_log_bigquery;
        }
      } elsif (
        # See if this looks like someone pretending to be Parse.ly
        req.http.User-Agent ~ "parse\.ly"
        && !(client.ip ~ cam_bot_parsely_ips)
      ) {
        # Conditionally log a percentage of these cases
        if (randombool(std.atoi(table.lookup(wally_dynamic_settings, "cam_crawler_test_logging_percent_parsely", "0")), 100) ) {
          set req.http.wally_log_event = "Crawler Fake - Parse.ly";
          call wally_log_bigquery;
        }
      }

    # Also allow some through that have verifiable IPs
    } elsif (
      client.ip ~ cam_bot_duckduckgo_ips
      || client.ip ~ cam_bot_true_anthem_ips
    ) {
      set req.http.Gannett-USAT-Request-CAM = "Disabled (Bot IP)";
    } elseif (client.ip ~ bluesky_crawler_ips) {
      set req.http.Gannett-USAT-Request-CAM = "Disabled (Allowed Crawler or Scraper)";
    }
    # We're taking User-Agent into account so we need to vary on it
    set req.http.vary_on_ua_for_cam = "Yes";
  }
}

sub recv_sanitize_cookie_header {
  # If you have functionality at shield or origin that needs cookies, follow the same pattern
  # as WALL*LY and add stash/share subroutines.  See the code there for a method to do this,
  # including the potentially useful wally_prep_cookie helper subroutine.

  # WALL*LY: Allow WALL*LY to save any cookies it needs. No we won't tell you where it's hiding the cookies.
  # WARNING: If this is ever removed, then gup_anonid will have to be preserved here like gup_clientid below.
  call wally_recv_cookie_stash;

  # GUPLIB: Save the gup_clientid cookie if it's set. WALL*LY is already preserving gup_anonid above.
  # (We're doing this here rather than guplib_recv_cookie_stash to prevent setting a new header)
  declare local var.gup_clientid STRING;
  if (std.strlen(req.http.Cookie:gup_clientid) > 0) {
    set var.gup_clientid = req.http.Cookie:gup_clientid;
  }

  # stash additional cookies to be preserved
  declare local var.gup_userjwt STRING;
  if (std.strlen(req.http.Cookie:gup_userjwt) > 0) {
    set var.gup_userjwt = req.http.Cookie:gup_userjwt;
  }

  declare local var.gnt_region STRING;
  if (req.http.Cookie:gnt_region) {
    set var.gnt_region = req.http.Cookie:gnt_region;
  }

  declare local var.gnt_bot STRING;
  if (req.http.Cookie:gnt_bot) {
    set var.gnt_bot = req.http.Cookie:gnt_bot;
  }

  declare local var.gnt_i STRING;
  if (req.http.Cookie:gnt_i) {
    set var.gnt_i = req.http.Cookie:gnt_i;
  }

  declare local var.gnt_w STRING;
  if (req.http.Cookie:gnt_w) {
    set var.gnt_w = req.http.Cookie:gnt_w;
  }

  declare local var.gnt_d STRING;
  if (req.http.Cookie:gnt_d) {
    set var.gnt_d = req.http.Cookie:gnt_d;
  }

  declare local var.gnt_ti STRING;
  if (req.http.Cookie:gnt_ti) {
    set var.gnt_ti = req.http.Cookie:gnt_ti;
  }

  declare local var._ga STRING;
  if (req.http.Cookie:_ga) {
    set var._ga = req.http.Cookie:_ga;
  }

  # EAT ALL THE COOKIES AND DON'T LEAVE ANY FOR ANYONE ELSE
  unset req.http.Cookie;

  if (std.strlen(var.gup_userjwt) > 0) {
    set req.http.Cookie:gup_userjwt = var.gup_userjwt;
  }

  # GUPLIB: If we saved the clientid cookie, restore it
  if (std.strlen(var.gup_clientid) > 0) {
    set req.http.Cookie:gup_clientid = var.gup_clientid;
  }

  if (std.strlen(var.gnt_region) > 0) {
    set req.http.Cookie:gnt_region = var.gnt_region;
  }

  if (std.strlen(var.gnt_bot) > 0) {
    set req.http.Cookie:gnt_bot = var.gnt_bot;
  }

  if (std.strlen(var.gnt_i) > 0) {
    set req.http.Cookie:gnt_i = var.gnt_i;
  }

  if (std.strlen(var.gnt_w) > 0) {
    set req.http.Cookie:gnt_w = var.gnt_w;
  }

  if (std.strlen(var.gnt_d) > 0) {
    set req.http.Cookie:gnt_d = var.gnt_d;
  }

  if (std.strlen(var.gnt_ti) > 0) {
    set req.http.Cookie:gnt_ti = var.gnt_ti;
  }

  if (std.strlen(var._ga) > 0) {
    set req.http.Cookie:_ga = var._ga;
  }

  # WALL*LY: Now have WALL*LY put back any cookies it needs into req.http.Cookie.
  call wally_recv_cookie_share;
}

sub error_page_restart_recv {
  if (req.restarts < 3) {
    unset req.http.X-AbVCfg;
    unset req.http.X-AbVariant;
    restart;
  } else {
    # redirect to error page instead when restart limit has been reached
    set req.http.x-Redir-Url = "https://" req.http.host "/errors/404/";
    error 702 req.http.x-Redir-Url;
  }
}

sub error_page_restart_deliver {
  if (req.restarts < 3) {
    unset req.http.X-AbVCfg;
    unset req.http.X-AbVariant;
    restart;
  } else {
    # redirect to error page instead when restart limit has been reached
    set resp.http.location = "https://" req.http.host "/errors/404/";
    set resp.status = 302;
  }
}

sub error_page_restart_deliver_check {
  if (resp.status == 404) {
    unset req.http.x-append-surrogate;
    set req.url = "/errors/404/";
    call error_page_restart_deliver;
  } elseif (resp.status >= 500) {
    unset req.http.x-append-surrogate;
    set req.url = "/errors/500/";
    call error_page_restart_deliver;
  }
}
