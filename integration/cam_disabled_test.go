package integration

import (
	"net/http"
	"testing"

	fastly "github.com/GannettDigital/go-fastly-tests"
)

func TestCamDisabled(t *testing.T) {
	t.<PERSON>()

	var tests = []fastly.Test{
		fastly.Status{
			Request: fastly.Request{
				Description: "Source Matters IP address PENG-23286 should have CAM disabled in www.usatoday.com",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/videos/news/2024/03/13/is-new-mexicos-unconventional-gun-control-approach-working/71964247007/",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers: http.Header{
					"gannett-geo-ip-override": []string{
						"**************",
					},
				},
			},
			Status: http.StatusOK,
		},
	}

	for _, test := range tests {
		test.Execute(t)
	}
}
