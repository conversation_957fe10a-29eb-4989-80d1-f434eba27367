table variables {
  "logging_percent_of_success" : "1" # successes to log
}

# these entries are being forced to return a 404 to prevent Google from indexing them.
table sitemap_return_404 {
  "/news_sitemap_home.xml" : "true",
  "/news_sitemap_life.xml" : "true",
  "/news_sitemap_life_books.xml" : "true",
  "/news_sitemap_life_entertainthis.xml" : "true",
  "/news_sitemap_life_movies.xml" : "true",
  "/news_sitemap_life_music.xml" : "true",
  "/news_sitemap_life_nation-now.xml" : "true",
  "/news_sitemap_life_people.xml" : "true",
  "/news_sitemap_life_theater.xml" : "true",
  "/news_sitemap_life_tv.xml" : "true",
  "/news_sitemap_money.xml" : "true",
  "/news_sitemap_money_business.xml" : "true",
  "/news_sitemap_money_careers.xml" : "true",
  "/news_sitemap_money_cars.xml" : "true",
  "/news_sitemap_money_columnist.xml" : "true",
  "/news_sitemap_money_markets.xml" : "true",
  "/news_sitemap_money_nation-now.xml" : "true",
  "/news_sitemap_money_personalfinance.xml" : "true",
  "/news_sitemap_news.xml" : "true",
  "/news_sitemap_news_college.xml" : "true",
  "/news_sitemap_news_health.xml" : "true",
  "/news_sitemap_news_humankind.xml" : "true",
  "/news_sitemap_news_nation.xml" : "true",
  "/news_sitemap_news_nation-now.xml" : "true",
  "/news_sitemap_news_politics.xml" : "true",
  "/news_sitemap_news_pr.xml" : "true",
  "/news_sitemap_news_world.xml" : "true",
  "/news_sitemap_nletter.xml" : "true",
  "/news_sitemap_nletter_weekly-elvis.xml" : "true",
  "/news_sitemap_opinion.xml" : "true",
  "/news_sitemap_sports.xml" : "true",
  "/news_sitemap_sports_boxing.xml" : "true",
  "/news_sitemap_sports_college.xml" : "true",
  "/news_sitemap_sports_columnist.xml" : "true",
  "/news_sitemap_sports_cycling.xml" : "true",
  "/news_sitemap_sports_fantasy.xml" : "true",
  "/news_sitemap_sports_ftw.xml" : "true",
  "/news_sitemap_sports_golf.xml" : "true",
  "/news_sitemap_sports_horseracing.xml" : "true",
  "/news_sitemap_sports_horses.xml" : "true",
  "/news_sitemap_sports_indycar.xml" : "true",
  "/news_sitemap_sports_mlb.xml" : "true",
  "/news_sitemap_sports_mls.xml" : "true",
  "/news_sitemap_sports_motor.xml" : "true",
  "/news_sitemap_sports_nascar.xml" : "true",
  "/news_sitemap_sports_nba.xml" : "true",
  "/news_sitemap_sports_ncaab.xml" : "true",
  "/news_sitemap_sports_ncaaf.xml" : "true",
  "/news_sitemap_sports_ncaaw.xml" : "true",
  "/news_sitemap_sports_nfl.xml" : "true",
  "/news_sitemap_sports_nhl.xml" : "true",
  "/news_sitemap_sports_olympics.xml" : "true",
  "/news_sitemap_sports_soccer.xml" : "true",
  "/news_sitemap_sports_tennis.xml" : "true",
  "/news_sitemap_sports_ufc.xml" : "true",
  "/news_sitemap_sports_wnba.xml" : "true",
  "/news_sitemap_tech.xml" : "true",
  "/news_sitemap_tech_reviewedcom.xml" : "true",
  "/news_sitemap_travel.xml" : "true",
  "/news_sitemap_travel_destinations.xml" : "true",
  "/news_sitemap_travel_experience.xml" : "true",
  "/news_sitemap_weather.xml" : "true",
}

table access_keys {
  "Basic YWRtaW46N2djN2Q0c2F4enZZTkpSNmV4MVdMZQ==": "admin",
  "Basic dGFuZ2ZyYWc6ODJnc2x0JjJiNCFm": "tangfrag",
}

table sitename_to_sitecode {
  "usatoday": "USAT",
  "usatodaysportsplus": "USPT"
}

table gannett_cdn_proxy {
  "dcc": "data-collection-config",
  "dcjs": "data-collection-javascript"
}

table ttp_dma_codes_usat {
  "658": "true",
  "702": "true",
  "669": "true",
  "617": "true",
  "515": "true",
  "510": "true",
  "535": "true",
  "558": "true",
  "504": "true",
  "508": "true",
  "566": "true",
  "584": "true",
  "544": "true",
  "560": "true",
  "524": "true",
  "522": "true",
  "567": "true",
  "592": "true",
  "561": "true",
  "534": "true",
  "539": "true"
}

table truetandem_celebrity_pages {   #needs to be converted to edge-dictionary pipeline
  "/entertainment/celebrities/anne-hathaway": "true",
  "/entertainment/celebrities/anne-hathaway/": "true",
  "/entertainment/celebrities/ben-affleck": "true",
  "/entertainment/celebrities/ben-affleck/": "true",
  "/entertainment/celebrities/blake-lively": "true",
  "/entertainment/celebrities/blake-lively/": "true",
  "/entertainment/celebrities/bruce-willis": "true",
  "/entertainment/celebrities/bruce-willis/": "true",
  "/entertainment/celebrities/glen-powell": "true",
  "/entertainment/celebrities/glen-powell/": "true",
  "/entertainment/celebrities/jennifer-aniston": "true",
  "/entertainment/celebrities/jennifer-aniston/": "true",
  "/entertainment/celebrities/jennifer-lopez": "true",
  "/entertainment/celebrities/jennifer-lopez/": "true",
  "/entertainment/celebrities/jenna-ortega": "true",
  "/entertainment/celebrities/jenna-ortega/": "true",
  "/entertainment/celebrities/kim-kardashian": "true",
  "/entertainment/celebrities/kim-kardashian/": "true",
  "/entertainment/celebrities/megan-fox": "true",
  "/entertainment/celebrities/megan-fox/": "true",
  "/entertainment/celebrities/millie-bobby-brown": "true",
  "/entertainment/celebrities/millie-bobby-brown/": "true",
  "/entertainment/celebrities/ryan-gosling": "true",
  "/entertainment/celebrities/ryan-gosling/": "true",
  "/entertainment/celebrities/ryan-reynolds": "true",
  "/entertainment/celebrities/ryan-reynolds/": "true",
  "/entertainment/celebrities/scarlet-johansson": "true",
  "/entertainment/celebrities/scarlet-johansson/": "true",
  "/entertainment/celebrities/sydney-sweeney": "true",
  "/entertainment/celebrities/sydney-sweeney/": "true",
  "/entertainment/celebrities/zendaya": "true",
  "/entertainment/celebrities/zendaya/": "true",
  "/entertainment/music/ariana-grande": "true",
  "/entertainment/music/ariana-grande/": "true",
  "/entertainment/music/billie-eilish": "true",
  "/entertainment/music/billie-eilish/": "true",
  "/entertainment/music/drake": "true",
  "/entertainment/music/drake/": "true",
  "/entertainment/music/eminem": "true",
  "/entertainment/music/eminem/": "true",
  "/entertainment/music/justin-bieber": "true",
  "/entertainment/music/justin-bieber/": "true",
  "/entertainment/music/olivia-rodrigo": "true",
  "/entertainment/music/olivia-rodrigo/": "true",
  "/entertainment/music/sabrina-carpenter": "true",
  "/entertainment/music/sabrina-carpenter/": "true",
  "/entertainment/music/selena-gomez": "true",
  "/entertainment/music/selena-gomez/": "true",
  "/entertainment/music/taylor-swift": "true",
  "/entertainment/music/taylor-swift/": "true"
}