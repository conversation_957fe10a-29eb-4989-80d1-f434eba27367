# Redirects have moved to: https://github.com/GannettDigital/EdgeRedirects
# Please open a PR against that REPO per the READMe to add a new redirect.

include "snippet::path_redirect_relative_host";
include "snippet::path_redirect_nonrelative_host";
include "snippet::path_redirect_offers";
include "snippet::host_redirect";

sub process_redirects {
  if (req.http.Gannett-Custom:site-name == "usatoday") {
    call process_weather_redirect;
    call process_path_redirect_relative_host;
    call process_path_redirect_nonrelative_host;
    call process_path_redirect_offers;
    call process_host_redirects;
  }

  call process_standalone_redirects;
  call process_gallery_and_video_redirects;
  call process_sports_sdi_redirects;
  call process_sportspolls_amway_redirects;

  call process_eu_redirects;

  if (req.http.Gannett-Custom:site-name == "usatodaysportsplus") {
    call process_usatodaysportsplus_redirects;
  }
}

sub process_path_redirect_relative_host {
  declare local var.x_url_path STRING;
  set var.x_url_path = std.tolower(req.url.path);
  if(table.lookup(path_redirect_relative_host, var.x_url_path)) {
    set req.http.x-Redir-Url = req.http.gannett-custom:x-gannett-protocol req.http.host table.lookup(path_redirect_relative_host, var.x_url_path);
    error 701 req.http.x-Redir-Url;
    set req.http.Gannett-Debug-Path-Item = table.lookup(path_redirect_relative_host, var.x_url_path) " redir";
    call shared_helpers_general_record_object_path;
  }
}

sub process_path_redirect_nonrelative_host {
  declare local var.x_url_path STRING;
  set var.x_url_path = std.tolower(req.url.path);
  if(table.lookup(path_redirect_nonrelative_host, var.x_url_path)) {
    set req.http.Gannett-Debug-Path-Item = table.lookup(path_redirect_nonrelative_host, var.x_url_path) " redir";
    call shared_helpers_general_record_object_path;

    # if the table element specifies the protocol, use that instead of the protocol relative to the request
    if(table.lookup(path_redirect_nonrelative_host, var.x_url_path) ~ "^http") {
      set req.http.x-Redir-Url = table.lookup(path_redirect_nonrelative_host, var.x_url_path);

    } else {
      set req.http.x-Redir-Url = req.http.gannett-custom:x-gannett-protocol table.lookup(path_redirect_nonrelative_host, var.x_url_path);
    }

    error 701 req.http.x-Redir-Url;
  }
}

sub process_path_redirect_offers {
  # redirect /x-xx promo code pages to special offer
  if(req.url.path ~ "(?i)^/(\w-\w{1,2})/?$"){
    declare local var.offer STRING;
    set var.offer = std.toupper(re.group.1);
    set req.http.Gannett-Debug-Path-Item = "redirect promo codes to special offer redir: " var.offer;
    call shared_helpers_general_record_object_path;
    set req.http.x-Redir-Url = "https://cm.usatoday.com/specialoffer?offer=" var.offer "&bar=top&barBuild=atoms-pid";
    error 701 req.http.x-Redir-Url;
  }
}

sub process_host_redirects {
  if(table.lookup(host_redirects, req.http.host)) {

    # if the table element specifies the protocol, use that instead of the protocol relative to the request
    if(table.lookup(path_redirect_nonrelative_host, req.url.path) ~ "^http") {
      set req.http.x-Redir-Url = table.lookup(host_redirects, req.http.host);
    } else {
      set req.http.x-Redir-Url = req.http.gannett-custom:x-gannett-protocol table.lookup(host_redirects, req.http.host);
    }
    error 701 req.http.x-Redir-Url;
  }
}

# unfortunatley, regex matches cannot be dynamic (ie a table lookup. So stuff other redirects here)
sub process_standalone_redirects {
  # redirect legacy roadblock pages
  if (
    req.url.path ~ "^/get-access/" ||
    req.url.path ~ "^/restricted/"
  ) {
    set req.http.x-Redir-Url = "https://subscribe." req.http.Gannett-Custom:site-apexdomain "/rr/restricted";
    error 701 req.http.x-Redir-Url;
  }
  # 301 old coupon widget URLs to the new /deals endpoints
  if (req.url.path ~ "^/money/coupons(/.*)") {
    set req.http.x-Redir-Url = "https://" req.http.host "/deals" re.group.1;
    set req.http.Gannett-Debug-Path-Item = "deals-coupons redirect";
    call shared_helpers_general_record_object_path;
    error 701 req.http.x-Redir-Url;
  }
  # odds pages that currently exist on UW will need to be permanently 301 redirected to the sportsdata experience
  if ( req.url.path ~ "^/sports/odds" ) {
    set req.http.Gannett-Debug-Path-Item = "Odds-redirect";
    call shared_helpers_general_record_object_path;
    set req.http.x-Redir-Url = "https://sportsdata.usatoday.com";
    error 701 req.http.x-Redir-Url;
  }

  # privacy notice update on Crosswords app launched with the incorrect url and we aren't able to change it
  if ( req.url.path ~ "^/privacy-policy-update-notice" ) {
    set req.http.Gannett-Debug-Path-Item = "privacy-update-redirect";
    call shared_helpers_general_record_object_path;
    set req.http.x-Redir-Url = "https://cm.usatoday.com/privacy-policy-update-notice";
    error 702 req.http.x-Redir-Url;
  }

  # Redirect old survivor-pool to the new URL endpoint
  if (req.url.path ~ "^/survivor-pool") {
    call process_sportscontests;
  }

  if ( req.url.path ~ "^/sports/(nfl|ncaaf|nba|ncaab|mlb|nhl)/odds" ) {
    declare local var.x_full_url STRING;
    set var.x_full_url = re.group.1;
    if (var.x_full_url ~ "(nfl|ncaaf)") {
      set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/football/" + var.x_full_url + "/odds";
    } elseif (var.x_full_url ~ "(nba|ncaab)") {
      set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/basketball/" + var.x_full_url + "/odds";
    } elseif (var.x_full_url ~ "mlb") {
      set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/baseball/" + var.x_full_url + "/odds";
    } else {
      set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/hockey/" + var.x_full_url + "/odds";
    }
    set req.http.Gannett-Debug-Path-Item = var.x_full_url + "Odds-redirect";
    call shared_helpers_general_record_object_path;
    error 701 req.http.x-Redir-Url;
  }

  # Redirect NCAA Coaches Poll pages from old to new URLs
  declare local var.x_sports STRING;
  if (req.url.path ~ "^/sports/ncaa(.+)/polls/coaches-poll(/.*)?\z") {
    set var.x_sports = re.group.1;
    set req.http.Gannett-Debug-Path-Item = "NCAA" + var.x_sports + " Coaches Poll redirect";
    call shared_helpers_general_record_object_path;
    if (var.x_sports == "b") {
      set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/basketball/ncaab/coaches-poll" + if (re.group.2, re.group.2, "");
    } elseif (var.x_sports == "f") {
      set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/football/ncaaf/coaches-poll" + if (re.group.2, re.group.2, "");
    } elseif (var.x_sports == "w") {
      set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/basketball/wncaab/coaches-poll" + if (re.group.2, re.group.2, "");
    } else {
      set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/baseball/cbb/coaches-poll" + if (re.group.2, re.group.2, "");
    }
    error 701 req.http.x-Redir-Url;
  } elseif (req.url.path ~ "^/sports/ncaa(.+)/polls/ap-poll(/.*)?\z") {
    set var.x_sports = re.group.1;
    set req.http.Gannett-Debug-Path-Item = "NCAA" + var.x_sports + " AP Poll redirect";
    call shared_helpers_general_record_object_path;
    if (var.x_sports == "b") {
      set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/basketball/ncaab/ap-poll" + if (re.group.2, re.group.2, "");
    } elseif (var.x_sports == "f") {
      set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/football/ncaaf/ap-poll" + if (re.group.2, re.group.2, "");
    } elseif (var.x_sports == "w") {
      set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/basketball/wncaab/ap-poll" + if (re.group.2, re.group.2, "");
    } else {
      set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/baseball/cbb/ap-poll" + if (re.group.2, re.group.2, "");
    }
    error 701 req.http.x-Redir-Url;
  } elseif (req.url.path ~ "^/sports/ncaaf/polls/college-football-playoff-rankings(/.*)?\z") {
    set req.http.Gannett-Debug-Path-Item = "NCAAF college-football-playoff-rankings redirect";
    call shared_helpers_general_record_object_path;
    set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/football/ncaaf/college-football-playoff-rankings" + if (re.group.1, re.group.1, "");
    error 701 req.http.x-Redir-Url;
  }

  #redirect all pages of the broken NFL arrests database to the new ACME tool experience
  if (req.url.path ~ "^/sports/nfl/arrests") {
    set req.http.Gannett-Debug-Path-Item = "NFL Arrest ACME";
    call shared_helpers_general_record_object_path;
    set req.http.x-Redir-Url = "https://databases.usatoday.com/nfl-arrests/";
    error 701 req.http.x-Redir-Url;
  } elseif (req.url.path ~ "^/sports/mlb/salaries") {
    set req.http.Gannett-Debug-Path-Item = "MLB Salaries ACME";
    call shared_helpers_general_record_object_path;
    set req.http.x-Redir-Url = "https://databases.usatoday.com/mlb-salaries/";
    error 701 req.http.x-Redir-Url;
  }
  #2020 Election Redirect
  if (req.url.path ~ "^(/elections/results/race/2020-11-03-ballot)(-)(initiative)(.*)") {
    set req.http.x-Redir-Url = "https://" + req.http.host re.group.1 + "_" + re.group.3 + re.group.4;
    error 701 req.http.x-Redir-Url;
  } elseif (req.url.path ~ "^(/elections/results/race/2020-11-03-state)(-)(house)(.*)") {
    set req.http.x-Redir-Url = "https://" + req.http.host re.group.1 + "_" + re.group.3 + re.group.4;
    error 701 req.http.x-Redir-Url;
  } elseif (req.url.path ~ "^(/elections/results/race/2020-11-03-state)(-)(senate)(.*)") {
    set req.http.x-Redir-Url = "https://" + req.http.host re.group.1 + "_" + re.group.3 + re.group.4;
    error 701 req.http.x-Redir-Url;
  }

  #2018 Election Redirect
   if (req.url.path ~ "^/data/elections/2018/results/senate") {
      set req.http.x-Redir-Url = "https://" + req.http.host + "/elections/results/2020-11-03/senate/";
      error 701 req.http.x-Redir-Url;
   }elseif (req.url.path ~ "^/data/elections/2018/results/house") {
      set req.http.x-Redir-Url = "https://" + req.http.host + "/elections/results/2020-11-03/us-house/";
      error 701 req.http.x-Redir-Url;
   }elseif (req.url.path ~ "^/data/elections/2018/results/governor") {
      set req.http.x-Redir-Url = "https://" + req.http.host + "/elections/results/2020-11-03/governor/";
      error 701 req.http.x-Redir-Url;
   }
  ##### File Ext. - .htm, .aspx, .ashx
   if ( req.url.ext == "ashx" || req.url.ext == "aspx" || req.url.ext == "htm") {
      set req.http.Gannett-Debug-Path-Item = "file ext redir";
      set req.url = "/errors/404/";
      call error_page_restart_recv;
   } else if ( req.url.path ~ "^/yourtake/") {
      set req.http.Gannett-Debug-Path-Item = "yourtake redir";
      call shared_helpers_general_record_object_path;
      set req.http.x-Redir-Url = req.http.gannett-custom:x-gannett-protocol req.http.host "/pages/interactives/your-take/";
      error 701 req.http.x-Redir-Url;
   } else if ( req.url.path ~ "^/experience/ski/") {
       set req.http.Gannett-Debug-Path-Item = "exp ski redir";
       call shared_helpers_general_record_object_path;
       set req.http.x-Redir-Url = req.http.gannett-custom:x-gannett-protocol "experience.usatoday.com/ski/";
       error 701 req.http.x-Redir-Url;
   } else if ( req.url.path ~ "^/experience/food-and-wine/") {
       set req.http.Gannett-Debug-Path-Item = "faw redir";
       call shared_helpers_general_record_object_path;
       set req.http.x-Redir-Url = req.http.gannett-custom:x-gannett-protocol "experience.usatoday.com/food-and-wine/";
       error 701 req.http.x-Redir-Url;
   } else if ( req.url.path ~ "^/picture-gallery/experience/weekend/" ||
            req.url.path ~ "^/experience/weekend/" ||
            req.url.path ~ "^/story/experience/weekend/" ||
            req.url.path ~ "^/video/experience/weekend/"
       ) {
       set req.http.Gannett-Debug-Path-Item = "weekend redir";
       call shared_helpers_general_record_object_path;
       set req.http.x-Redir-Url = req.http.gannett-custom:x-gannett-protocol "experience.usatoday.com/weekend/";
       error 701 req.http.x-Redir-Url;
   } else if ( req.url.path ~ "^/picture-gallery/experience/cruise/" ||
            req.url.path ~ "^/experience/cruise/"
   ) {
     set req.http.Gannett-Debug-Path-Item = "cruise redir";
     call shared_helpers_general_record_object_path;
     set req.http.x-Redir-Url = req.http.gannett-custom:x-gannett-protocol "experience.usatoday.com/cruise/";
     error 701 req.http.x-Redir-Url;
   } else if ( req.url.path ~ "^/story/experience/destinations/" ||
        req.url.path ~ "^/experience/destinations/"
   ) {
     set req.http.Gannett-Debug-Path-Item = "destinations redir";
     call shared_helpers_general_record_object_path;
     set req.http.x-Redir-Url = req.http.gannett-custom:x-gannett-protocol "experience.usatoday.com/destinations/";
     error 701 req.http.x-Redir-Url;
   } else if ( req.url.path ~ "^/picture-gallery/experience/america/" ||
        req.url.path ~ "^/experience/america/" ||
        req.url.path ~ "^/story/experience/america/" ||
        req.url.path ~ "^/video/experience/america/" ||
        req.url.path ~ "^/experience/south/" ||
        req.url.path ~ "^/picture-gallery/experience/south/" ||
        req.url.path ~ "^/videos/experience/south/"
   ) {
     set req.http.Gannett-Debug-Path-Item = "america redir";
     call shared_helpers_general_record_object_path;
     set req.http.x-Redir-Url = req.http.gannett-custom:x-gannett-protocol "experience.usatoday.com/america/";
     error 701 req.http.x-Redir-Url;
   } else if ( req.url.path ~ "^/picture-gallery/experience/las-vegas/" ||
        req.url.path ~ "^/experience/las-vegas/" ||
        req.url.path ~ "^/story/experience/las-vegas/" ||
        req.url.path ~ "^/video/experience/las-vegas/"
   ) {
     set req.http.Gannett-Debug-Path-Item = "las-vegas redir";
     call shared_helpers_general_record_object_path;
     set req.http.x-Redir-Url = req.http.gannett-custom:x-gannett-protocol "experience.usatoday.com/las-vegas/";
     error 701 req.http.x-Redir-Url;
   } else if ( req.url.path ~ "^/picture-gallery/experience/beach/" ||
        req.url.path ~ "^/experience/beach/"
   ) {
     set req.http.Gannett-Debug-Path-Item = "beach redir";
     call shared_helpers_general_record_object_path;
     set req.http.x-Redir-Url = req.http.gannett-custom:x-gannett-protocol "experience.usatoday.com/beach/";
     error 701 req.http.x-Redir-Url;
   } else if ( req.url.path ~ "^/picture-gallery/experience/caribbean/" ||
        req.url.path ~ "^/experience/caribbean/" ||
        req.url.path ~ "^/story/experience/caribbean/" ||
        req.url.path ~ "^/videos/experience/caribbean/"
   ) {
     set req.http.Gannett-Debug-Path-Item = "carribbean redir";
     call shared_helpers_general_record_object_path;
     set req.http.x-Redir-Url = req.http.gannett-custom:x-gannett-protocol "experience.usatoday.com/caribbean/";
     error 701 req.http.x-Redir-Url;
  } else if (req.url.path == "/sports/ncaaw/polls/" || req.url.path == "/sports/ncaaw/polls") {
     set req.http.Gannett-Debug-Path-Item = "sports ncaaw redir";
     call shared_helpers_general_record_object_path;
     set req.http.x-Redir-Url = req.http.gannett-custom:x-gannett-protocol req.http.host "/sports/ncaaw/polls/coaches-poll/"; // move to EdgeRedirects
     error 701 req.http.x-Redir-Url;
   } else if (req.url.path == "/sports/ncaab/polls/" || req.url.path == "/sports/ncaab/polls") {
     set req.http.Gannett-Debug-Path-Item = "sports ncaab redir";
     call shared_helpers_general_record_object_path;
     set req.http.x-Redir-Url = req.http.gannett-custom:x-gannett-protocol req.http.host "/sports/ncaab/polls/coaches-poll/"; // move to EdgeRedirects
     error 701 req.http.x-Redir-Url;
   } else if ( req.url.path ~ "^/repurposing/samsung/" ) {
    set req.http.Gannett-Debug-Path-Item = "samsung repurposing redir";
    call shared_helpers_general_record_object_path;
    set req.http.x-Redir-Url = req.http.gannett-custom:x-gannett-protocol "www.gannett-cdn.com/test/1x1.png";
    error 701 req.http.x-Redir-Url;
  } else if ( req.url.path ~ "^/money/lookup/index" ||
            req.url.path ~ "^/money/lookup/stocks" ) {
    set req.http.Gannett-Debug-Path-Item = "stock ticker redir";
    call shared_helpers_general_record_object_path;
    set req.http.x-Redir-Url = req.http.gannett-custom:x-gannett-protocol "www.usatoday.com/money/";
    error 701 req.http.x-Redir-Url;
  }

  # redirect old interactives to new location /interactives/sponsor-story/*
  if (req.url.path ~ "^/pages/interactives/sponsor-story/") {
    set req.http.Gannett-Debug-Path-Item = "legacy /pages/interactives/sponsor-story/ redir";
    call shared_helpers_general_record_object_path;
    set req.http.x-Redir-Url = req.http.gannett-custom:x-gannett-protocol req.http.host regsub(req.url.path, "^/pages", "");
    error 701 req.http.x-Redir-Url;
  }

  # redirect old marketplace jobs urls to new destination
  if (
    req.url.path == "/jobs/" ||
    req.url.path == "/jobsearch/" ||
    req.url.path == "/marketplace/jobsearch/"
  ) {
    set req.http.x-Redir-Url = "/marketplace/jobs/";
    error 701 req.http.x-Redir-Url;
  }

  # redirect old marketplace cars urls to new destination
  if (
    req.url.path == "/cars/"
  ) {
    set req.http.x-Redir-Url = "/marketplace/cars/";
    error 701 req.http.x-Redir-Url;
  }

  # normalize video embed URLs
  if (req.url ~ "^/videos?/embed/(.+)") {
    set req.http.x-Redir-Url = "/embed/video/" + re.group.1;
    error 701 req.http.x-Redir-Url;
  }
}

sub process_gallery_and_video_redirects {
  if (req.url.path ~ "^/media/cinematic/(gallery|video)/([^/]*)/") {
    set req.http.x-Redir-Url = req.http.gannett-custom:x-gannett-protocol req.http.host if (re.group.1 == "video" , "/videos", "/picture-gallery") "/0/0/0/0/0/" re.group.2 "/";
    error 701 req.http.x-Redir-Url;
  } elseif (req.url.path ~ "^/(picture-gallery|videos)/0/0/0/[a-zA-Z0-9-]+/(\d{2,})/?$") {
    set req.http.x-Redir-Url = req.http.gannett-custom:x-gannett-protocol req.http.host "/" re.group.1 "/0/0/0/0/0/" re.group.2 "/";
    error 701 req.http.x-Redir-Url;
  }
}

sub process_sports_sdi_redirects {
  # Preview Events
  if (req.url.path ~ "^/sports/(nfl|mlb|nhl|nba|wnba|ncaaf|ncaab|wncaab)/event/20[0-9]{2}/(.*)/(preview|boxscore|recap|matchup|summary)") {
    set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/" table.lookup(league_mapping, re.group.1) "/" re.group.1 "/summary/" re.group.2;
    # must call shared_helpers_general_record_object_path AFTER re.group vars are used otherwise they will get reset
    set req.http.Gannett-Debug-Path-Item = "sdi preview|boxscore|recap redirect";
    call shared_helpers_general_record_object_path;
    error 701 req.http.x-Redir-Url;
  }

# NCAAB Teams Redirects
  if(req.url.path ~ "^/sports/ncaab/([^/]*)/") {
    declare local var.ncaab_team STRING;
    set var.ncaab_team = table.lookup(ncaab_mapping, re.group.1);
    if(var.ncaab_team != "") {
      if(req.url.path ~ "^/sports/ncaab/([^/]*)/schedule/$") {
        set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/basketball/ncaab/teamschedule/" var.ncaab_team;
        error 701 req.http.x-Redir-Url;
      }
      if(req.url.path ~ "^/sports/ncaab/([^/]*)/statistics/$") {
        set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/basketball/ncaab/teamstatistics/" var.ncaab_team;
        error 701 req.http.x-Redir-Url;
      }
      if(req.url.path ~ "^/sports/ncaab/([^/]*)/roster/$") {
        set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/basketball/ncaab/teamroster/" var.ncaab_team;
        error 701 req.http.x-Redir-Url;
      }
      if(req.url.path ~ "^/sports/ncaab/([^/]*)/injuries/all/$") {
        set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/basketball/ncaab/injuries";
        error 701 req.http.x-Redir-Url;
      }
      if(req.url.path ~ "^/sports/ncaab/([^/]*)/$") {
        set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/basketball/ncaab/team/" var.ncaab_team;
        error 701 req.http.x-Redir-Url;
      }
    }
  }

  # NCAAF Teams Redirects
  if(req.url.path ~ "^/sports/ncaaf/([^/]*)/") {
    declare local var.ncaaf_team STRING;
    set var.ncaaf_team = table.lookup(ncaaf_mapping, re.group.1);
    if(var.ncaaf_team != "") {
      if(req.url.path ~ "^/sports/ncaaf/([^/]*)/schedule/$") {
        set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/football/ncaaf/teamschedule/" var.ncaaf_team;
        error 701 req.http.x-Redir-Url;
      }
      if(req.url.path ~ "^/sports/ncaaf/([^/]*)/statistics/$") {
        set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/football/ncaaf/teamstatistics/" var.ncaaf_team;
        error 701 req.http.x-Redir-Url;
      }
      if(req.url.path ~ "^/sports/ncaaf/([^/]*)/roster/$") {
        set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/football/ncaaf/teamroster/" var.ncaaf_team;
        error 701 req.http.x-Redir-Url;
      }
      if(req.url.path ~ "^/sports/ncaaf/([^/]*)/injuries/all/$") {
        set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/football/ncaaf/injuries";
        error 701 req.http.x-Redir-Url;
      }
      if(req.url.path ~ "^/sports/ncaaf/([^/]*)/$") {
        set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/football/ncaaf/team/" var.ncaaf_team;
        error 701 req.http.x-Redir-Url;
      }
    }
  }

  # NHL Redirects
  if(req.url.path ~ "^/sports/nhl/([^/]*)/") {
    declare local var.nhl_team STRING;
    set var.nhl_team = table.lookup(nhl_mapping, re.group.1);
    if(var.nhl_team != "") {
      if(req.url.path ~ "^/sports/nhl/([^/]*)/schedule/$") {
        set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/hockey/nhl/teamschedule/" var.nhl_team;
        error 701 req.http.x-Redir-Url;
      }
      if(req.url.path ~ "^/sports/nhl/([^/]*)/statistics/$") {
        set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/hockey/nhl/teamstatistics/" var.nhl_team;
        error 701 req.http.x-Redir-Url;
      }
      if(req.url.path ~ "^/sports/nhl/([^/]*)/roster/$") {
        set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/hockey/nhl/teamroster/" var.nhl_team;
        error 701 req.http.x-Redir-Url;
      }
      if(req.url.path ~ "^/sports/nhl/([^/]*)/transactions/$") {
        set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/hockey/nhl/transactions";
        error 701 req.http.x-Redir-Url;
      }
      if(req.url.path ~ "^/sports/nhl/([^/]*)/injuries/all/$") {
        set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/hockey/nhl/injuries";
        error 701 req.http.x-Redir-Url;
      }
      if(req.url.path ~ "^/sports/nhl/([^/]*)/$") {
        set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/hockey/nhl/team/" var.nhl_team;
        error 701 req.http.x-Redir-Url;
      }
    }
  }

  # NBA Redirects
  if(req.url.path ~ "^/sports/nba/([^/]*)/") {
    declare local var.nba_team STRING;
    set var.nba_team = table.lookup(nba_mapping, re.group.1);
    if(var.nba_team != "") {
      if(req.url.path ~ "^/sports/nba/([^/]*)/schedule/$") {
        set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/basketball/nba/teamschedule/" var.nba_team;
        error 701 req.http.x-Redir-Url;
      }
      if(req.url.path ~ "^/sports/nba/([^/]*)/statistics/$") {
        set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/basketball/nba/teamstatistics/" var.nba_team;
        error 701 req.http.x-Redir-Url;
      }
      if(req.url.path ~ "^/sports/nba/([^/]*)/roster/$") {
        set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/basketball/nba/teamroster/" var.nba_team;
        error 701 req.http.x-Redir-Url;
      }
      if(req.url.path ~ "^/sports/nba/([^/]*)/transactions/$") {
        set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/basketball/nba/transactions";
        error 701 req.http.x-Redir-Url;
      }
      if(req.url.path ~ "^/sports/nba/([^/]*)/injuries/all/$") {
        set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/basketball/nba/injuries";
        error 701 req.http.x-Redir-Url;
      }
      if(req.url.path ~ "^/sports/nba/([^/]*)/$") {
        set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/basketball/nba/team/" var.nba_team;
        error 701 req.http.x-Redir-Url;
      }
    }
  }

  # MLB Redirects
  if(req.url.path ~ "^/sports/mlb/([^/]*)/") {
    declare local var.mlb_team STRING;
    set var.mlb_team = table.lookup(mlb_mapping, re.group.1);
    if(var.mlb_team != "") {
      if(req.url.path ~ "^/sports/mlb/([^/]*)/schedule/$") {
        set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/baseball/mlb/teamschedule/" var.mlb_team;
        error 701 req.http.x-Redir-Url;
      }
      if(req.url.path ~ "^/sports/mlb/([^/]*)/statistics/$") {
        set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/baseball/mlb/teamstatistics/" var.mlb_team;
        error 701 req.http.x-Redir-Url;
      }
      if(req.url.path ~ "^/sports/mlb/([^/]*)/roster/$") {
        set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/baseball/mlb/teamroster/" var.mlb_team;
        error 701 req.http.x-Redir-Url;
      }
      if(req.url.path ~ "^/sports/mlb/([^/]*)/transactions/$") {
        set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/baseball/mlb/transactions";
        error 701 req.http.x-Redir-Url;
      }
      if(req.url.path ~ "^/sports/mlb/([^/]*)/injuries/all/$") {
        set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/baseball/mlb/injuries";
        error 701 req.http.x-Redir-Url;
      }
      if(req.url.path ~ "^/sports/mlb/([^/]*)/$") {
        set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/baseball/mlb/team/" var.mlb_team;
        error 701 req.http.x-Redir-Url;
      }
    }
  }

  # NFL Redirects
  if(req.url.path ~ "^/sports/nfl/([^/]*)/") {
    declare local var.nfl_team STRING;
    set var.nfl_team = table.lookup(nfl_mapping, re.group.1);
    if(var.nfl_team != "") {
      if(req.url.path ~ "^/sports/nfl/([^/]*)/schedule/$") {
        set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/football/nfl/teamschedule/" var.nfl_team;
        error 701 req.http.x-Redir-Url;
      }
      if(req.url.path ~ "^/sports/nfl/([^/]*)/statistics/$") {
        set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/football/nfl/teamstatistics/" var.nfl_team;
        error 701 req.http.x-Redir-Url;
      }
      if(req.url.path ~ "^/sports/nfl/([^/]*)/roster/$") {
        set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/football/nfl/teamroster/" var.nfl_team;
        error 701 req.http.x-Redir-Url;
      }
      if(req.url.path ~ "^/sports/nfl/([^/]*)/transactions/$") {
        set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/football/nfl/transactions";
        error 701 req.http.x-Redir-Url;
      }
      if(req.url.path ~ "^/sports/nfl/([^/]*)/injuries/all/$") {
        set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/football/nfl/injuries";
        error 701 req.http.x-Redir-Url;
      }
      if(req.url.path ~ "^/sports/nfl/([^/]*)/$") {
        set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/football/nfl/team/" var.nfl_team;
        error 701 req.http.x-Redir-Url;
      }
    }
  }

  # Sports Leagues Redirects
  if(req.url.path ~ "^/sports/(nfl|nba|mlb|nhl)/(scores|injuries|teams|transactions|statistics|standings|schedule)/") {
    declare local var.sport STRING;
    set var.sport = table.lookup(league_mapping, re.group.1);
    if(var.sport != "") {
      if(re.group.2 == "statistics") {
        set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/" var.sport "/" re.group.1 "/stats";
      } else {
        set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/" var.sport "/" re.group.1 "/" re.group.2;
      }
      error 701 req.http.x-Redir-Url;
    }
  }

  # NCAA Sports Leagues
  if(req.url.path ~ "^/sports/(ncaaf|ncaab|wncaab)/(scores|injuries|teams|statistics|standings|schedule)/") {
    declare local var.sport2 STRING;
    set var.sport2 = table.lookup(league_mapping, re.group.1);
    if(var.sport2 != "") {
      if(re.group.2 == "statistics" ) {
        set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/" var.sport2 "/" re.group.1 "/stats";
      } else {
        set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/" var.sport2 "/" re.group.1 "/" re.group.2;
      }
      error 701 req.http.x-Redir-Url;
    }
  }

  # Golf Redirects
  if(req.url.path ~ "^/sports/golf/?$" || req.url.path ~ "^/sports/golf/schedule") {
    set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/golf/pga/schedule";
    error 701 req.http.x-Redir-Url;
  }
  if(req.url.path ~ "^/sports/golf/standings") {
    set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/golf/pga/rankings";
    error 701 req.http.x-Redir-Url;
  }
  if(req.url.path ~"^/sports/golf/leaderboard") {
    set req.http.x-Redir-Url = "https://www.pgatour.com/leaderboard.html?cid=USAT";
    error 701 req.http.x-Redir-Url;
  }

  # Soccer Redirects
  if(req.url.path ~ "^/sports/soccer/(mls|wc|wwc|epl)/(scores|teams|standings|schedule)") {
    if(re.group.2 == "schedule" ) {
      set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/soccer/" re.group.1 "/scores";
    } else {
      set req.http.x-Redir-Url = "https://sportsdata.usatoday.com/soccer/" re.group.1 "/" re.group.2;
    }
    error 701 req.http.x-Redir-Url;
  }

  # WNBA
  if(req.url.path ~ "^/sports/wnba/([^/]*)/") {
    declare local var.wnba_team STRING;
    set var.wnba_team = table.lookup(wnba_mapping, re.group.1);
    if(var.wnba_team != "") {
      if(req.url.path ~ "^/sports/wnba/([^/]*)/schedule") {
        set req.http.x-Redir-Url = "https://usatoday.sportsdirectinc.com/basketball/wnba-teams.aspx?page=/data/wnba/teams/schedule/team" var.wnba_team ".html";
        error 701 req.http.x-Redir-Url;
      }
      if(req.url.path ~ "^/sports/wnba/([^/]*)/statistics") {
        set req.http.x-Redir-Url = "https://usatoday.sportsdirectinc.com/basketball/wnba-teams.aspx?page=/data/wnba/teams/stats/2019/stats" var.wnba_team ".html";
        error 701 req.http.x-Redir-Url;
      }
      if(req.url.path ~ "^/sports/wnba/([^/]*)/roster") {
        set req.http.x-Redir-Url = "https://usatoday.sportsdirectinc.com/basketball/wnba-teams.aspx?page=/data/wnba/teams/rosters/roster" var.wnba_team ".html";
        error 701 req.http.x-Redir-Url;
      }
    }
    if(re.group.1 == "scores" || re.group.1 == "schedule") {
      set req.http.x-Redir-Url = "https://usatoday.sportsdirectinc.com/sports-scores/WNBA-Scores-Matchups.aspx";
      error 701 req.http.x-Redir-Url;
    }
    if (re.group.1 == "standings" || re.group.1 == "statistics") {
      set req.http.x-Redir-Url = "https://usatoday.sportsdirectinc.com/basketball/wnba-" re.group.1 ".aspx?page=/data/wnba/" re.group.1 "/" re.group.1 ".html&hf=off";
      error 701 req.http.x-Redir-Url;
    }
    if (re.group.1 == "teams") {
      set req.http.x-Redir-Url = "https://usatoday.sportsdirectinc.com/basketball/wnba-teams.aspx?page=/data/wnba/teams/teams.html";
      error 701 req.http.x-Redir-Url;
    }
    if (re.group.1 == "injuries") {
      set req.http.x-Redir-Url = "https://usatoday.sportsdirectinc.com/basketball/wnba-injuries.aspx?page=/data/wnba/injury/injuries.html";
      error 701 req.http.x-Redir-Url;
    }
  }

  # Nascar
  if(req.url.path ~ "^/sports/nascar/results") {
    set req.http.x-Redir-Url = "https://usatoday.sportsdirectinc.com/motorsports/nascar/nascar-main.aspx?page=/data/nascar/this_weeks_race.html";
    error 701 req.http.x-Redir-Url;
  }
  if(req.url.path ~ "^/sports/nascar/schedule") {
    set req.http.x-Redir-Url = "https://usatoday.sportsdirectinc.com/motorsports/nascar/nascar-schedule.aspx?page=/data/nascar/schedule.html";
    error 701 req.http.x-Redir-Url;
  }
  if(req.url.path ~"^/sports/nascar/drivers") {
    set req.http.x-Redir-Url = "https://usatoday.sportsdirectinc.com/motorsports/nascar/nascar-drivers.aspx?page=/data/nascar/drivers/A_drivers.html";
    error 701 req.http.x-Redir-Url;
  }

  # Tennis
  if(req.url.path ~ "^/sports/tennis/results") {
    set req.http.x-Redir-Url = "https://usatoday.sportsdirectinc.com/tennis/tennis-results.aspx?page=/data/atp/results/results.html&hf=off";
    error 701 req.http.x-Redir-Url;
  }
  if(req.url.path ~ "^/sports/tennis/schedule") {
    set req.http.x-Redir-Url = "https://usatoday.sportsdirectinc.com/tennis/tennis-schedule.aspx?page=/data/atp/schedule/schedule.html";
    error 701 req.http.x-Redir-Url;
  }
  if(req.url.path ~"^/sports/tennis/players") {
    set req.http.x-Redir-Url = "https://usatoday.sportsdirectinc.com/tennis/tennis-money-leaders.aspx?page=/data/atp/rankings/2020/earnings_rankings_20200316.html";
    error 701 req.http.x-Redir-Url;
  }

  # Motor Sports
  if(req.url.path ~ "^/sports/motor-sports/schedule/") {
    set req.http.x-Redir-Url = "https://usatoday.sportsdirectinc.com/motorsports/motorsports.aspx";
    error 701 req.http.x-Redir-Url;
  }
}

# redirect no longer supported amway-coaches-poll URLs to new URL format
sub process_sportspolls_amway_redirects {
  if(req.url.path ~ "^/sports/ncaaf/polls/amway-coaches-poll/") {
    set req.http.x-Redir-Url = "https://www.usatoday.com" std.replace(req.url.path, "amway-coaches-poll", "coaches-poll");
    error 701 req.http.x-Redir-Url;
   } elseif (req.url.path ~ "^/sports/college/ncaaf/polls/" || req.url.path == "/sports/ncaaf/polls/" || req.url.path == "/sports/ncaaf/polls") {
     set req.http.x-Redir-Url = req.http.gannett-custom:x-gannett-protocol req.http.host "/sports/ncaaf/polls/coaches-poll/";
     error 701 req.http.x-Redir-Url;
   }
}

# previous WordPress sportspolls site sportspolls.usatoday.com was serving both http and https traffic and Google indexed both versions
# we need to be able to execute these redirects before the force SSL redirect in order reduce the amount of total redirects to 1
# as to not lose SEO value
sub process_sportspolls_redirects {
  if (req.http.host ~ "^(?:ci-|staging-|origin-staging-(?:[0-9]-)?|cam-)?sportspolls\.usatoday\.com$") {
    // redirects for sportspolls.usatoday.com --> www.usatoday.com/sports/:league/polls/:poll
    if (req.url.path == "/") {
      set req.http.Gannett-Debug-Path-Item = "sports polls redir";
      call shared_helpers_general_record_object_path;
      set req.http.x-Redir-Url = "https://www.usatoday.com/sports/ncaaf/polls/coaches-poll/";
      error 701 req.http.x-Redir-Url;
    } else if (req.url.path ~ "^/ncaa/football/polls/coaches-poll") {
      set req.http.Gannett-Debug-Path-Item = "sports polls redir";
      call shared_helpers_general_record_object_path;
      set req.http.x-Redir-Url = "https://www.usatoday.com/sports/ncaaf/polls/coaches-poll/";
      error 701 req.http.x-Redir-Url;
    } else if (req.url.path ~ "^/ncaa/football/polls/cfp-poll") {
      set req.http.Gannett-Debug-Path-Item = "sports polls redir";
      call shared_helpers_general_record_object_path;
      set req.http.x-Redir-Url = "https://www.usatoday.com/sports/ncaaf/polls/college-football-playoff-rankings/";
      error 701 req.http.x-Redir-Url;
    } else if (req.url.path ~ "^/ncaa/football/polls/ap-poll") {
      set req.http.Gannett-Debug-Path-Item = "sports polls redir";
      call shared_helpers_general_record_object_path;
      set req.http.x-Redir-Url = "https://www.usatoday.com/sports/ncaaf/polls/ap-poll/";
      error 701 req.http.x-Redir-Url;
    } else if (req.url.path ~ "^/ncaa/basketball-men/polls/coaches-poll") {
      set req.http.Gannett-Debug-Path-Item = "sports polls redir";
      call shared_helpers_general_record_object_path;
      set req.http.x-Redir-Url = "https://www.usatoday.com/sports/ncaab/polls/coaches-poll/";
      error 701 req.http.x-Redir-Url;
    } else if (req.url.path ~ "^/ncaa/basketball-men/polls/ap-poll") {
      set req.http.Gannett-Debug-Path-Item = "sports polls redir";
      call shared_helpers_general_record_object_path;
      set req.http.x-Redir-Url = "https://www.usatoday.com/sports/ncaab/polls/ap-poll/";
      error 701 req.http.x-Redir-Url;
    } else if (req.url.path ~ "^/ncaa/basketball-women/polls/coaches-poll") {
      set req.http.Gannett-Debug-Path-Item = "sports polls redir";
      call shared_helpers_general_record_object_path;
      set req.http.x-Redir-Url = "https://www.usatoday.com/sports/ncaaw/polls/coaches-poll/";
      error 701 req.http.x-Redir-Url;
    } else if (req.url.path ~ "^/ncaa/basketball-women/polls/ap-poll") {
      set req.http.Gannett-Debug-Path-Item = "sports polls redir";
      call shared_helpers_general_record_object_path;
      set req.http.x-Redir-Url = "https://www.usatoday.com/sports/ncaaw/polls/ap-poll/";
      error 701 req.http.x-Redir-Url;
    } else if (req.url.path ~ "^/ncaa/baseball/polls/coaches-poll") {
      set req.http.Gannett-Debug-Path-Item = "sports polls redir";
      call shared_helpers_general_record_object_path;
      set req.http.x-Redir-Url = "https://www.usatoday.com/sports/ncaa-baseball/polls/coaches-poll/";
      error 701 req.http.x-Redir-Url;
    } else {
      if (req.url.path != "/robots.txt" && req.url.path !~ "^/.*sitemap\.xml$") {
        set req.http.Gannett-Debug-Path-Item = "sports polls 404 redir";
        call shared_helpers_general_record_object_path;
        set req.http.x-Redir-Url = "https://www.usatoday.com/errors/404/";
        error 701 req.http.x-Redir-Url;
      }
    }
  }
}

# for handling of zipcode based accuweather redirects to /weather/
sub process_weather_redirect {
  if (req.url.path == "/weather/" || req.url.path == "/weather") {
    declare local var.weather-redirect STRING;
    declare local var.zip-prefix STRING;
    declare local var.zip-suffix STRING;
    set var.zip-prefix = substr(client.geo.postal_code, 0, 3);
    set var.zip-suffix = substr(client.geo.postal_code, 3, 2) "=";
    set var.weather-redirect = if (table.lookup(tangent_supported_country_zip, client.geo.country_code), table.lookup(gnt_weather_redirects, var.zip-prefix), "");
    # fallback to McLean, VA = 22102
    if (var.weather-redirect == "") {
      set var.zip-prefix = "221";
      set var.zip-suffix = "02=";
      set var.weather-redirect = table.lookup(gnt_weather_redirects, var.zip-prefix);
    }
    if (var.weather-redirect != "") {
      # extract zip weather data
      set var.weather-redirect = std.strstr(var.weather-redirect, var.zip-suffix);
      set var.weather-redirect = std.replace_suffix(var.weather-redirect, std.strstr(var.weather-redirect, "|"), "");
      set var.weather-redirect = std.replace_prefix(var.weather-redirect, var.zip-suffix, "");
      # perform expansion if data was further truncated
      if (var.weather-redirect ~ "\*") {
        set var.weather-redirect = std.replace(var.weather-redirect, "*", "/current-weather/") "_pc";
      }
      set req.http.x-Redir-Url = "https://www.accuweather.com/en/us/" var.weather-redirect "?lang=en-us&partner=web_usatoday_gannett_news";
      error 702 req.http.x-Redir-Url;
    } else {
      set req.http.x-Redir-Url = "https://" req.http.host "/";
      error 701 req.http.x-Redir-Url;
    }
  }
}

# redirects for usatodaysportsplus.com only
sub process_usatodaysportsplus_redirects {
  # Until the Sports+ Subscriber Guide is ready, redirect the following:
  if (req.url.path ~ "^/subscriberguide/") {
    set req.http.Gannett-Debug-Path-Item = "USPT Subscriber redirect";
    call shared_helpers_general_record_object_path;
    set req.http.x-Redir-Url = "https://help.usatoday.com/member-benefits";
    error 701 req.http.x-Redir-Url;
  } elseif (req.url.path ~ "^/newsletters/") {
    set req.http.Gannett-Debug-Path-Item = "USPT Newsletters redirect";
    call shared_helpers_general_record_object_path;
    set req.http.x-Redir-Url = "https://profile.usatoday.com/newsletters/manage/";
    error 701 req.http.x-Redir-Url;
  # assets, misc paths, and resources with file extentions
  } elseif (
    req.url.path ~ "^/story/" ||
    req.url.path ~ "^/videos/" ||
    req.url.path ~ "^/picture-gallery/" ||
    req.url.path ~ "^/errors/" ||
    req.url.path ~ "^/search/" ||
    req.url.path ~ "^/elections/" ||
    req.url.path ~ "^/storytelling/" ||
    req.url.path ~ "^/in-depth/" ||
    req.url.path ~ "^/staff/" ||
    req.url.path ~ "^/contact/" ||
    req.url.path ~ "^/tangsvc/" ||
    req.url.path ~ "^/.well-known/" ||
    # keep sitemaps on usatodaysportsplus.com
    (
      req.url.ext != "" &&
      req.url.path !~ "/news-sitemap\.xml$" &&
      req.url.path !~ "/web-sitemap-index\.xml$" &&
      req.url.path !~ "/video-sitemap-index\.xml$"
    )
  ) {
    set req.http.Gannett-Debug-Path-Item = "USPT asset redirect";
    call shared_helpers_general_record_object_path;
    set req.http.x-Redir-Url = "https://www.usatoday.com" req.url;
    error 701 req.http.x-Redir-Url;
  # fronts
  } elseif (req.url.ext == "") {
    set req.http.Gannett-Debug-Path-Item = "USPT front redirect";
    call shared_helpers_general_record_object_path;
    declare local var.redirect STRING;
    set var.redirect = table.lookup(usatodaysportsplus_redirects, std.tolower(req.url.path));
    if(std.strlen(var.redirect) > 0) {
      if (std.strlen(req.url.qs) > 0) {
        set var.redirect = var.redirect "?" req.url.qs;
      }
      set req.http.x-Redir-Url = "https://www.usatoday.com" var.redirect;
    } elseif (req.url.path == "/") {
      set req.http.x-Redir-Url = "https://www.usatoday.com/story/sports/2022/09/01/usa-today-sports-plus-app-now-available-to-network-subscribers/7941469001/";
      error 702 req.http.x-Redir-Url;
    } elseif (req.url.path ~ "^/college-basketball/men/") {
      set req.http.x-Redir-Url = "https://www.usatoday.com/sports/ncaab/";
    } elseif (req.url.path ~ "^/college-basketball/women/") {
      set req.http.x-Redir-Url = "https://www.usatoday.com/sports/ncaaw/";
    } elseif (req.url.path ~ "^/college-football/") {
      set req.http.x-Redir-Url = "https://www.usatoday.com/sports/ncaaf/";
    } else {
      set req.http.x-Redir-Url = "https://www.usatoday.com/sports" req.url;
    }
    error 701 req.http.x-Redir-Url;
  }
}

table usatodaysportsplus_redirects {
  "/college-basketball/": "/sports/",
  "/mlb/cleveland-indians/": "/sports/mlb/cleveland-guardians/",
  "/nfl/la-rams/": "/sports/nfl/los-angeles-rams/",
  "/nfl/super-bowl/": "/super-bowl/",
  "/nfl/washington/": "/sports/nfl/washington-commanders/"
}
