package integration

import (
	"net/http"
	"testing"

	fastly "github.com/GannettDigital/go-fastly-tests"
)

func TestCetFrontEndStatic(t *testing.T) {
	t.<PERSON>()

	var tests = []fastly.Test{
		fastly.Header{
			Request: fastly.Request{
				Description: "should serve a 200, set ttl to 1 year (31536000) and select cet-front-end-static storage.googleapis.com backend for migrated /pages/interacives/ page",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/pages/interactives/fb-barometer/",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers: http.Header{
					"Accept-Encoding": []string{
						"br",
					},
				},
			},
			Status: http.StatusOK,
			Headers: http.Header{
				"Gannett-Debug-Path": []string{
					"cet-front-end-static",
					"ttl: 31536000.00",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "should serve a 200, set ttl to 1 year (31536000) and select cet-front-end-static storage.googleapis.com backend for migrated /pages/interacives/ page",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/pages/interactives/fb-barometer/",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers: http.Header{
					"Accept-Encoding": []string{
						"gzip",
					},
				},
			},
			Status: http.StatusOK,
			Headers: http.Header{
				"Gannett-Debug-Path": []string{
					"cet-front-end-static",
					"ttl: 31536000.00",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Status{
			Request: fastly.Request{
				Description: "should serve 404 for unsupported /pages/interactives/ page with Tangent supported browser",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/pages/interactives/blahblah404/",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusNotFound,
		},
		fastly.Status{
			Request: fastly.Request{
				Description: "should serve 404 for unsupported /pages/interactives/ page with Tangent unsupported browser",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/pages/interactives/blahblah404/",
				UA:          UserAgentMacOSChromeTangentUnsupported,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusNotFound,
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "should serve a 200 and set approriate headers for /pages/interactive/assets/ resource",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/pages/interactives/assets/js/main-5018.3.0.min.js",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers: http.Header{
					"Accept-Encoding": []string{
						"gzip",
					},
				},
			},
			Status: http.StatusOK,
			Headers: http.Header{
				"Content-Type": []string{
					"application/javascript",
				},
				"Gannett-Debug-Path": []string{
					"cet-front-end-static",
					"ttl: 31536000.00",
				},
				"X-Content-Type-Options": []string{
					"nosniff",
				},
				"Vary": []string{
					"Accept-Encoding",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
	}

	for _, test := range tests {
		test.Execute(t)
	}
}
